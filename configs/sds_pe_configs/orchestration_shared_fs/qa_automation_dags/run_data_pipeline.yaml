trigger_data_pipeline_old:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON><PERSON> CR
    retries: 1
    start_date: 2024-01-01
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    update_backfill_info:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [start_task]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/variables'
      method: "POST"
      headers: 
        Content-Type: "application/json"
      data: '{"key": "SDS_BACKFILL_CONFIG", "value": "{\"start_date\": \"2025-02-02\", \"end_date\": \"2025-02-04\", \"pipeline_id\": \"SDS_EI_PIPELINE\", \"dag_id\": \"sds_ei_start\"}"}'
      extra_options:
        verify: false
    enable_backfill_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [update_backfill_info]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/sds_backfill_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
      do_xcom_push: true
    enable_all_dependant_dag:
      operator: airflow.operators.bash.BashOperator
      dependencies: [update_backfill_info]
      bash_command: >
       curl -s -X GET -k "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/variables/SDS_ORCHESTRATOR_CONFIG" 
       -H "Content-Type: application/json" -u "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}:{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}"
       |python -c 'import sys, json; data = json.loads(json.loads(sys.stdin.read())["value"]); 
       values = set(d for dep in data["SDS_EI_PIPELINE"]["dependencies"] for d in dep["dependencies"]); 
       values.remove("sds_ei_start"); print("\n".join(values))'| 
       while read value; 
       do curl -X PATCH -k  "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/dags/$value" 
       -H "Content-Type: application/json" -u "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}:{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}"
       -d '{"is_paused": false}'; 
       done
    disable_start_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [enable_all_dependant_dag]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/sds_ei_start'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": true}'
      extra_options:
        verify: false
    enable_pg_olap_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [update_backfill_info]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/puppy_graph_olap_pipeline'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    enable_mediator_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [update_backfill_info]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/mediator'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    trigger_backfill_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_backfill_dag, disable_start_dag, enable_pg_olap_dag, enable_mediator_dag]
      trigger_dag_id: "sds_backfill_dag"
      wait_for_completion: true
    check_pipeline_data_run_completed:
      startup_timeout_seconds: 300
      dependencies: [trigger_backfill_dag]
      feature_files:
      - src/platform/features/check_dag_run_completed.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      image: prevalentai/sds-platform-apps-qa-automation:<%PLATFORM_QA_IMAGE_VERSION%>
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      run_prerequisites: false
      generate_report: false
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true      
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        EXEC_ENVIRONMENT: "<%EXEC_ENVIRONMENT%>"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        ICEBERG_WAREHOUSE: "<%DATALAKE_URI%>/<%KUBE_NAMESPACE%>/iceberg/"
        ICEBERG_CATALOG_TYPE: "<%ICEBERG_CATALOG_TYPE%>"
        ICEBERG_CATALOG_URI: "<%ICEBERG_CATALOG_URI%>"
        HADOOP_FS_IMPL: "<%HADOOP_FS_IMPL%>"
        HADOOP_FS_END_POINT: "<%HADOOP_FS_ENDPOINT%>"
        SERVICE_ACCOUNT: "<%SERVICE_ACCOUNT%>"
        IMAGE: "prevalentai/sds-platform-apps-qa-automation:<%PLATFORM_QA_IMAGE_VERSION%>"
        EXECUTOR_MEMORY: "2g"
        DRIVER_MEMORY: "2g"
        EXECUTOR_INSTANCES: "2"
        EXECUTOR_CORES: "1"
        APP_NAME: "run_data_pipeline"
        UPDATED_AT: "{{data_interval_end.int_timestamp}}"
        DOMAIN_URL: "{{macros.custom_macros.get_env('DOMAIN_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        DATALAKE_URI: "s3a://{{macros.custom_macros.get_env('S3_DATALAKE_BUCKET_NAME')}}"
        PYICEBERG_CATALOG__DEFAULT__URI: "{{macros.custom_macros.get_env('PYICEBERG_CATALOG__DEFAULT__URI')}}"
        KEYCLOAK_SSL_VERIFY: "{{macros.custom_macros.get_env('KEYCLOAK_SSL_VERIFY', True)}}"
        DATALAKE_LOG_URI: "<%LOG_URI%>"
        AIRFLOW_API_ENDPOINT: http://airflow-webserver.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/orchestration
        INTERNAL_URL: http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080
        KGQL_URL: http://sds-platform-apps-graphql.<%KUBE_NAMESPACE%>.svc.cluster.local:8000/graphql
        KG_DISABLE_CACHE: "{{macros.custom_macros.get_env('KG_DISABLE_CACHE', True)}}"
        REQUESTS_CA_BUNDLE: /mnt/CABundle/pythoncacerts
      volume_mounts:
      - mount_path: /mnt/CABundle
        sub_path: null
        name: pythoncacerts
        read_only: true
      volumes:
      - name: pythoncacerts
        configs:
          secret:
            defaultMode: 511
            secretName: pythoncacerts
      secrets:
      - deploy_type: env
        deploy_target: SELENIUM_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumUsername
      - deploy_type: env
        deploy_target: SELENIUM_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumPassword
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: AIRFLOW_API_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiUsername
      - deploy_type: env
        deploy_target: AIRFLOW_API_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
      tags:
      - check-dag-run
    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [check_pipeline_data_run_completed]
