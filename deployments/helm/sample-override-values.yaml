# =======================================================================
# =======================================================================
# This section contains values for the Deployer Helm chart itself.
# This values control the Release bundleversion and manages its 
# modules based on the Release bundle version.
#
# Key configurations in this section include:
# - Release bundle version of the mono repository
#
# =======================================================================

# bundle-version: 3.6.1-20250219-085810281796694


# =======================================================================
# Individual Module Configuration Section
# =======================================================================
# This section contains override values for each module managed by the 
# Deployer. Each modules values should be placed directly under
# its chart name. All the possible module names are listed in this values file below.
# We jus need to uncomment the same and override the configurations as you need.
#
# =======================================================================
# ========================================================================
# Individual jobs configuration over rides section
# =======================================================================

# jobOverrides:
#   sds_nifi_shared_copy_job:
#     enabled: 
#     bundle-version:
#     image: 
#       repository: prevalentai/sds-platform-apps-deployment-utils
#       tag:
#     command: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []
  
#   sds_airflow_variable_update_job:
#     enabled:
#     bundle-version:
#     command: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []
  
#   sds_airflow_shared_copy_job:
#     enabled:
#     bundle-version:
#     command: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []

#   ei_config_client_deploy_job:
#     enabled:
#     bundle-version:
#     initJob:
#       image: 
#         repository: prevalentai/sds-client-ibx-configs
#         tag: 
#     mainContainer:
#       image: 
#         repository: prevalentai/sds-platform-apps-deployment-utils
#         tag: 
#       ei_version:
#       env_type:
#     image:
#       repository: prevalentai/sds-client-ibx-configs
#     autoscaling:
#       enabled:
#     initCommand: []
#     command: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []

#   spark_config_copy_job:
#     enabled:
#     bundle-version:
#     command: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []

#   sds_client_dag_deploy_job:
#     bundle-version:
#     enabled:
#     image:
#       repository: prevalentai/sds-client-ibx-orchestration
#       tag:
#       pullPolicy:
#     command: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []


