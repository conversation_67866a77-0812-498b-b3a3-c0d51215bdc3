{{- if .Values.jobOverrides.ei_config_client_deploy_job.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: ei-config-client-deploy-{{ .Release.Name}}
  labels:
    job: {{ .Values.labelJob }}
    {{- include "k8jobs.labels" . | nindent 4 }}
spec:
  backoffLimit: {{ default .Values.backoffLimit .Values.jobOverrides.ei_config_client_deploy_job.backoffLimit }}
  activeDeadlineSeconds: {{ default .Values.activeDeadlineSeconds .Values.jobOverrides.ei_config_client_deploy_job.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ default .Values.ttlSecondsAfterFinished .Values.jobOverrides.ei_config_client_deploy_job.ttlSecondsAfterFinished }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        sidecar.istio.io/inject: "false"
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "k8jobs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}      
      initContainers:
        - name: mgmntfilecopy
          image: "{{ (index .Values "jobOverrides" "ei_config_client_deploy_job" "initJob" "image" "repository") }}:{{ include "eiconfigclient.getImageTag" . }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with index .Values "jobOverrides" "ei_config_client_deploy_job" }}
          command: {{ tpl (toYaml .initCommand) $ | nindent 12 }}
          {{- end }}  
          volumeMounts:
            - name: shared-volume
              mountPath: /tmp

      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ (index .Values "jobOverrides" "ei_config_client_deploy_job" "mainContainer" "image" "repository") }}:{{ (index .Values "jobOverrides" "ei_config_client_deploy_job" "mainContainer" "image" "tag") | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with index .Values "jobOverrides" "ei_config_client_deploy_job" }}
          command: {{ tpl (toYaml .command) $ | nindent 12 }}
          {{- end }}              
          env:
            {{- include "custom_ei_config_deploy_job_environment" . | indent 10 }}
            - name: CONFIG_MANAGER_HOSTNAME
              value: http://{{ .Values.mgmnt_api_svc_name | default "mgmtapis" }}.{{ tpl .Values.NAMESPACE . }}.svc.cluster.local:8080/
            - name: AUTH_TOKEN_URL
              value: https://{{ .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ tpl .Values.NAMESPACE . }}/protocol/openid-connect/token
            - name: CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: clientSecret
            - name: CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: clientId
            - name: ENV_TYPE
              value: {{ .Values.global.CONFIG_CONTEXT }}
            - name: EI_VERSION
              value: {{ .Values.jobOverrides.ei_config_client_deploy_job.mainContainer.ei_version }}
            - name: EM_VERSION
              value: {{ .Values.jobOverrides.ei_config_client_deploy_job.mainContainer.em_version }}
            - name: PE_VERSION
              value: {{ .Values.jobOverrides.ei_config_client_deploy_job.mainContainer.image.tag }}
          {{- if .Values.args }}
          args: {{ tpl (toYaml .Values.args) . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml (index .Values "jobOverrides" "ei_config_client_deploy_job" "resources") | nindent 12 }}
          volumeMounts:
            - name: shared-volume
              mountPath: /tmp/
      volumes:
        - name: shared-volume
          emptyDir: {}
{{- end }}
