{"Host": {"caption": "Host", "description": "An independent compute instance where we have visibility or management of the operating system.", "attributes": {"project": {"caption": "Project", "description": "Field enriched from solution", "examples": "test", "group": "enrichment", "ui_visibility": true, "type": "string", "source": "test", "derived_field": false, "candidate_key": false}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows:If the difference between latest inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other non cloud sources like VM, EDR, etc., it is set to 30 days.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "type": "string", "derived_field": true, "enable_hiding": false}, "location_country": {"description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "location_city": {"description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "login_last_user": {"caption": "Last Logged in User", "description": "Last user logged in to the host.", "examples": "james", "group": "entity_specific", "type": "string", "source": ["Windows Security Logs"], "ui_visibility": false}, "ad_sam_account_name": {"caption": "AD SAM Account Name", "description": "SAM account name belonging to the machine account associated with this host.", "examples": "SOF-GABBGEO-MOY$", "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "ui_visibility": false}, "role": {"caption": "Role", "description": "Defines the primary function, responsibility, or purpose of an entity within the system or organization. \\nDistinct values are Domain Controller, DNS Server, Proxy, etc.", "group": "entity_specific", "type": "string"}, "vlan": {"caption": "VLAN", "description": "Virtual Local Area Network. It is a logical segmentation of a computer network, allowing devices to be grouped together in a way that they appear to be on the same physical network, even if they are located on different physical networks.", "group": "entity_specific", "type": "string"}, "infoblox_ref": {"caption": "Infoblox REF", "description": "Refer to an object reference or a reference to an object in infoblox.", "group": "source_specific", "type": "string", "data_structure": "list", "candidate_key": true}, "vlan_updated": {"caption": "Is VLAN Updated", "description": "Vlan is changed or not.", "group": "entity_specific", "type": "string"}, "archival_flag": {"caption": "Archival Flag", "enable_hiding": true, "ui_visibility": false, "dashboard_identifier": {"EI": {}}}, "cloud_instance_id": {"type": "string"}, "cloud_bound_ports": {"type": "string"}, "is_cloud_container_privileged": {"type": "string"}, "asset_criticality": {"caption": "Asset Criticality", "description": "Indicates the the value of an asset to the organization's operations and reputation.\nThis is a numeric score ranging between 0 and 1 based on multiple criteria such as if a host is present in a production environment or the host is providing a business critical services etc.\n Parameters taken into consideration for the current implementation are, purpose of the asset and type of the host.", "type": "double", "range_selection": true, "min": 0, "max": 1, "step_interval": 0.01, "group": "enrichment"}, "domain_score": {"caption": "Host Domain Risk Score", "description": "Risk Score derived for the Host based on whether host is domain joined or not.\nDomain joined devices will have policies and controls in place, these might be lacked in a non-domain joined devices there by increasing the risk factor.", "group": "enrichment", "type": "double", "range_selection": true}, "sensitive_role_host_score": {"caption": "Host Role Risk Score", "description": "Risk Score derived for the Host based the assignment of sensitive role for the host.\nThis factor contributes to the risks from potential availability and criticality of a sensitive infrastructure host.", "group": "enrichment", "type": "double", "range_selection": true}}}, "Person": {"caption": "Person Entity Inventory", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "attributes": {"activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between latest inventory update date and last active date is less than the specified inactivity period of 30 days, the entity is considered active; otherwise, it is inactive.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "type": "string", "derived_field": true, "enable_hiding": false}, "location_country": {"description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "location_city": {"description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "job_function": {"caption": "Job Function", "description": "Job function details of an employee", "examples": "Account Executive", "group": "entity_specific", "ui_visibility": true, "type": "string", "source": "BambooHR", "derived_field": false, "candidate_key": false}, "aad_created_date": {"description": "Date when Azure AD object was created."}, "phone_number": {"caption": "Phone Number", "description": "Phone number of an employee", "examples": "Account Executive", "group": "enrichment", "ui_visibility": true, "type": "string", "source": "test", "derived_field": false, "candidate_key": false}, "emp_level_score": {"caption": "Employee Level Risk Score", "description": "Risk Score derived for the Person based on the Employee Level.", "group": "enrichment", "type": "double", "range_selection": true}, "emp_type_score": {"caption": "Employee Type Risk Score", "description": "Risk Score derived for the Person based employee type.\nA contractor is considered to be of higher risk than permanent employee ", "group": "enrichment", "type": "double", "range_selection": true}}}, "Identity": {"caption": "Identity", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used", "extends": "", "attributes": {"activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between latest inventory update date and last active date is less than the specified inactivity period of 30 days, the entity is considered active; otherwise, it is inactive.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "type": "string", "derived_field": true, "enable_hiding": false}, "location_country": {"description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "location_city": {"description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "account_name": {"caption": "Account Name", "description": "The name/login used to access the system or service.", "examples": ["<EMAIL>", "d1624f15-b162-4ae1-a964-3c72ae096c85"], "group": "entity_specific", "ui_visibility": true, "type": "string", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender"], "candidate_key": true}, "account_type": {"caption": "Account Type", "description": "The type of account being accessed.", "examples": ["Service Account", "MACHINE_ACCOUNT", "NORMAL_USER_ACCOUNT"], "group": "entity_specific", "ui_visibility": true, "type": "string", "source": ["MS Active Directory", "Saviynt IGA"], "candidate_key": false}, "aad_created_date": {"description": "Date when Azure AD object was created."}}}, "Vulnerability": {"caption": "Vulnerability", "description": "The Vulnerability Dictionary defines attributes defined for Vulnerability entity derived from multiple sources.", "attributes": {"location_country": {"description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "location_city": {"description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard."}, "title": {"caption": "Title", "description": "Title of the vulnerability.", "examples": "Accellion FTA OS Command Injection Vulnerability", "group": "entity_specific", "ui_visibility": true, "type": "string", "source": ["Qualys"], "derived_field": false, "candidate_key": false}}}, "Cloud Compute": {"caption": "Cloud Compute", "description": "Cloud Compute entity encompasses both compute resources and its services within the broader cloud computing paradigm.", "attributes": {"activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 30 days.", "group": "common", "type": "string", "derived_field": true, "enable_hiding": false}}}, "Account": {"caption": "Account", "description": "An independent compute instance where we have visibility or management of the operating system.", "attributes": {"activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between latest inventory update date and last active date is less than the specified inactivity period of 30 days, the entity is considered active; otherwise, it is inactive.", "group": "common", "type": "string", "derived_field": true, "enable_hiding": false}, "role_score": {"caption": "Account Role Risk Score", "description": "Risk Score derived for the account based on its role.\nAccount is considered to be risky if the IDP has marked the assigned role as critical.", "group": "enrichment", "type": "double", "range_selection": true}}}, "Application": {"attributes": {"activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between latest inventory update date and last active date is less than the specified inactivity period of 30 days, the entity is considered active; otherwise, it is inactive.", "group": "common", "type": "string", "derived_field": true, "enable_hiding": false}}}}