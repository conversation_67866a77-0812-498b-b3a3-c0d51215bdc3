ARG AWS_KUBE_AZ_BASE_IMAGE="prevalentai/devops-utils:4-1-0-kubectl1.27.0-awscliv2-azcpv10-azcli-bookworm-12.10-20250428-slim"

FROM ${AWS_KUBE_AZ_BASE_IMAGE} AS runnerbase

USER root

ARG DAGS_DIR="/opt/airflow/sds/dags"
ARG COMMONS_DIR="/opt/airflow/sds/commons"
ARG PLUGINS_DIR="/opt/airflow/sds/plugins"
ARG client_code="sol"

RUN mkdir -p ${DAGS_DIR} ${COMMONS_DIR} ${PLUGINS_DIR}

COPY ./dags/${client_code} ${DAGS_DIR}/${client_code}

COPY ./plugins/${client_code} ${PLUGINS_DIR}/${client_code}

COPY ./commons/${client_code} ${COMMONS_DIR}/${client_code}

RUN cd ${DAGS_DIR} \
    && tar -cvzf ${client_code}.tgz ${client_code} \
    && rm -rf ${client_code} \
    && cd ${COMMONS_DIR} \
    && tar -cvzf ${client_code}.tgz ${client_code} \
    && rm -rf ${client_code} \
    && cd ${PLUGINS_DIR} \
    && tar -cvzf ${client_code}.tgz ${client_code} \
    && rm -rf ${client_code} \
    && chown -R sdsuser /opt/airflow/sds

USER sdsuser

ENTRYPOINT ["/usr/bin/dumb-init", "--", "/entrypoint"]

CMD ["bash"]