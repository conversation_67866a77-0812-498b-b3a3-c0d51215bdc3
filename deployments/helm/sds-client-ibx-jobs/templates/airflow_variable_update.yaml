{{- if .Values.jobOverrides.sds_airflow_variable_update_job.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: airflow-variable-update-{{ .Release.Name}}
  labels:
    job: {{ .Values.labelJob }}
    {{- include "k8jobs.labels" . | nindent 4 }}
spec:
  backoffLimit: {{ default .Values.backoffLimit .Values.jobOverrides.sds_airflow_variable_update_job.backoffLimit }}
  activeDeadlineSeconds: {{ default .Values.activeDeadlineSeconds .Values.jobOverrides.sds_airflow_variable_update_job.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ default .Values.ttlSecondsAfterFinished .Values.jobOverrides.sds_airflow_variable_update_job.ttlSecondsAfterFinished }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "k8jobs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ (index .Values "jobOverrides" "image" "repository") }}:{{ include "airflowvariableupdate.getImageTag" . }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with index .Values "jobOverrides" "sds_airflow_variable_update_job" }}
          command: {{ tpl (toYaml .command) $ | nindent 12 }}
          {{- end }}            

          envFrom:
          {{- include "job_environment_from" . | default "\n  []" | indent 10 }}
         ## Only handled the AWS secrets here, we may need to extend this for Azure as well.
          env:
            {{- include "custom_airflow_variable_job_environment" . | indent 10 }}
            - name: AIRFLOW_CONN_AIRFLOW_DB
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: airflowDbConnUrl
            - name: AIRFLOW__CORE__SQL_ALCHEMY_CONN
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: airflowDbConnUrl
            - name: AIRFLOW__CORE__FERNET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: fernetKey
          {{- include "job_environment" . | indent 10 }}
          resources:
            {{- toYaml (index .Values "jobOverrides" "sds_airflow_variable_update_job" "resources") | nindent 12 }}
          volumeMounts:
            {{ include "extra_volumes_mounts" . | indent 10 }}
      volumes:
        {{- include "extra_volumes" . | indent 10 }}

{{- end }}