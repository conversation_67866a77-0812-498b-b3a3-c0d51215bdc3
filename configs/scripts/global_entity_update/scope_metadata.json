{"scopes": [{"scope_definition": "Entity Specific - String", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["cloud_resource_type", "hardware_chassis_type", "hardware_bios_manufacturer", "hardware_model"], "field_type": "string", "default_expression": "COALESCE({field},'No Data')", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Source Specific - String", "field_group": "sourceSpecificProperties", "entities": ["Host"], "attribute_list": ["crowdstrike_product_type_desc"], "field_type": "string", "default_expression": "COALESCE({field},'No Data')", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Entity Specific - Boolean", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["edr_fully_functional", "jail_broken"], "field_type": "string", "default_expression": "COALESCE({field},False)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Assessment Fields - <PERSON><PERSON><PERSON>", "field_group": "sourceSpecificProperties", "entities": ["Host"], "attribute_list": ["crowdstrike_fim_policy_status", "crowdstrike_firewall_status", "crowdstrike_sensor_uninstall_protection_status", "crowdstrike_prevention_policy_status", "crowdstrike_sensor_update_policy_status", "crowdstrike_remote_response_status", "crowdstrike_host_retention_status", "crowdstrike_global_config_status", "crowdstrike_content_update_status", "crowdstrike_device_control_status", "aad_management_status", "aad_compliance_status", "aws_instance_has_iam_role"], "field_type": "boolean", "default_expression": "COALESCE({field},False)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "VM Tracking Method - Array", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["vm_tracking_method"], "field_type": "array<string>", "default_expression": "CASE WHEN size({field}) > 0 THEN {field} ELSE array('No Data') END", "fieldsSpec": {"computationPhase": "inter"}}]}