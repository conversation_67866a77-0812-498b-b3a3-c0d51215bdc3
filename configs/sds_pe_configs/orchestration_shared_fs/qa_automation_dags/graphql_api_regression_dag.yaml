graphql_api_regression_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: Athira_Damodaran
    retries: 1
    start_date: 2024-01-01
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      feature_files: ['src/platform/features/GraphQL_API.feature']
      image: prevalentai/sds-platform-apps-qa-automation:<%PLATFORM_QA_IMAGE_VERSION%>
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      run_prerequisites: false
      generate_report: true
      report_base_path: "<%QA_REPORT_URI%>/behave/"
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true      
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        EXEC_ENVIRONMENT: "<%EXEC_ENVIRONMENT%>"
        TEST_ENV: "<%KUBE_NAMESPACE%>"
        DEBUG_MODE: "true"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        ICEBERG_WAREHOUSE: "<%DATALAKE_URI%>/iceberg/"
        ICEBERG_CATALOG_TYPE: "<%ICEBERG_CATALOG_TYPE%>"
        ICEBERG_CATALOG_URI: "<%ICEBERG_CATALOG_URI%>"
        HADOOP_FS_IMPL: "<%HADOOP_FS_IMPL%>"
        HADOOP_FS_END_POINT: "<%HADOOP_FS_ENDPOINT%>"
        SERVICE_ACCOUNT: "<%SERVICE_ACCOUNT%>"
        IMAGE: prevalentai/sds-platform-apps-qa-automation:<%PLATFORM_QA_IMAGE_VERSION%>
        SDM_SCHEMA_NAME: "<%SDM_SCHEMA_NAME%>"
        EI_SCHEMA_NAME: "<%EI_SCHEMA_NAME%>"
        SRDM_SCHEMA_NAME: "<%SRDM_SCHEMA_NAME%>"
        EXECUTOR_MEMORY: "6g"
        DRIVER_MEMORY: "4g"
        EXECUTOR_INSTANCES: "4"
        EXECUTOR_CORES: "1"
        APP_NAME: "graphql-regression"
        UPDATED_AT: "{{data_interval_end.int_timestamp}}"
        DOMAIN_URL: "{{macros.custom_macros.get_env('DOMAIN_URL')}}"
        SELENIUM_SDS3_URL: "{{macros.custom_macros.get_env('SELENIUM_SDS3_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        AIRFLOW_CONN_ADMIN_API_BASE_URL: "{{macros.custom_macros.get_env('AIRFLOW_CONN_ADMIN_API_BASE_URL')}}"
        AIRFLOW_CONN_RCM_ADMIN_API: "{{macros.custom_macros.get_env('AIRFLOW_CONN_RCM_ADMIN_API')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        MANAGEMENT_API_UI_MAPPING_ENDPOINT: "{{macros.custom_macros.get_env('MANAGEMENT_API_UI_MAPPING_ENDPOINT')}}"
        DATALAKE_URI: "<%DATALAKE_URI%>"
        PYICEBERG_CATALOG__DEFAULT__URI: "{{macros.custom_macros.get_env('PYICEBERG_CATALOG__DEFAULT__URI')}}"
        KEYCLOAK_SSL_VERIFY: "{{macros.custom_macros.get_env('KEYCLOAK_SSL_VERIFY', True)}}"
        DATALAKE_LOG_URI: "<%LOG_URI%>"
        AIRFLOW_API_ENDPOINT: http://airflow-webserver.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/orchestration
        INTERNAL_URL: http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080
        KGQL_URL: http://sds-platform-apps-graphql.<%KUBE_NAMESPACE%>.svc.cluster.local:8000/graphql
        KGQL_UPDATE_SCHEMA_URL: http://sds-platform-apps-graphql.<%KUBE_NAMESPACE%>.svc.cluster.local:8000/graphql/update-schema
        HIVE_METASTORE_URL: thrift://hivemetastore.<%KUBE_NAMESPACE%>.svc.cluster.local:9083
        KG_DISABLE_CACHE: "{{macros.custom_macros.get_env('KG_DISABLE_CACHE', True)}}"
        TENANT_ID: "{{macros.custom_macros.get_env('TENANT_ID')}}"
        AZURE_MANAGED_IDENTITY_ID: "{{macros.custom_macros.get_env('AZURE_MANAGED_IDENTITY_ID')}}"
      secrets:
      - deploy_type: env
        deploy_target: SELENIUM_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumUsername
      - deploy_type: env
        deploy_target: SELENIUM_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumPassword
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: AIRFLOW_API_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiUsername
      - deploy_type: env
        deploy_target: AIRFLOW_API_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
      - deploy_type: env
        deploy_target: PUPPYGRAPH_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: puppyGraphUsername
      - deploy_type: env
        deploy_target: PUPPYGRAPH_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: puppyGraphPassword
      volume_mounts:
      - mount_path: /mnt/CABundle
        sub_path: null
        name: pythoncacerts
        read_only: true
      volumes:
      - name: pythoncacerts
        configs:
          secret:
            defaultMode: 511
            secretName: pythoncacerts
  tasks:
    start_task:
      operator: airflow.operators.empty.EmptyOperator
    
    load_data_to_iceberg:
      dependencies:
      - start_task
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - graphql-pre-check-regression
    update_graph_schema:
      dependencies:
      - load_data_to_iceberg
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - olap-pipeline-pre-requisites
    trigger_olap_pipeline:
      dependencies:
      - update_graph_schema
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - graphql-trigger-olap
    post_configs_to_emAPI:
      dependencies:
      - update_graph_schema
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - graphql-config-setup
    run_graphql_regression:
      dependencies:
      - trigger_olap_pipeline
      - post_configs_to_emAPI
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - graphql-regression
    api_authentication_check:
      dependencies:
      - run_gremlin_comparison
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - Authentication
      feature_files:
      - src/platform/features/authentication.feature
      trigger_rule: "all_done"
    redis_cache_check:
      dependencies:
      - api_authentication_check
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - redis
      feature_files:
      - src/platform/features/redis_key_check.feature
      trigger_rule: "all_done"
    graphql_post_activities:
      dependencies:
      - redis_cache_check
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - graphql-post-check
      trigger_rule: "all_done"
    run_gremlin_comparison:
      dependencies:
      - run_graphql_regression
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - gremlin
      feature_files:
      - src/platform/features/Gremlin_compare.feature
      trigger_rule: "all_done"
    end_task:
      dependencies:
      - graphql_post_activities
      operator: airflow.operators.empty.EmptyOperator
      trigger_rule: "all_done"
