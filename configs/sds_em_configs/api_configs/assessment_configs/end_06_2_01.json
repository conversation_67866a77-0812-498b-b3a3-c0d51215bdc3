{"id": "END-06.2-01", "title": "Devices with EDR agents have reported to crowdstrike cloud within the last 7 days", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 9, "is_active": true, "description": "This assessment checks whether devices with EDR agents have reported to the CrowdStrike cloud within the last 7 days. Regular reporting ensures timely detection of threats and device visibility.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'Crowdstrike Last Report SLA Duration' is within the last 7 days"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_last_report_sla_status,crowdstrike_last_report_sla_duration,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_last_report_sla_duration <=7", "finding_primary_key": "p_id", "finding_title": "CrowdStrike not reported in 7 days", "finding_config": [{"title": "Crowdstrike Last Report SLA Duration", "expression": "crowdstrike_last_report_sla_duration", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-06.2"]}}