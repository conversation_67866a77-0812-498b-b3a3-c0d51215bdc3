{"microsoft_azure__defender_device_software_vuln_delta": "true", "infoblox_onprem_ddi__host": "true", "microsoft_azure__resource_details": "true", "microsoft_azure__security_resources": "true", "infoblox_onprem_ddi__host_enrichment": "true", "microsoft_azure__security_assessment": "true", "ms_azure_ad__user_registration_details": "true", "aws__iam_list_users": "true", "aws__describe_config_rules": "true", "aws__sh_get_enabled_standards": "true", "aws__sh_describe_standards_controls": "true", "aws__sh_list_security_control_definitions": "true", "aws__sh_list_standards_control_associations": "true", "aws__iam_security_center_permission_set_assignment": "true", "open_data__epss_data": "true", "microsoft_azure__ad_user": "true", "aws__cloudtrail": "true", "microsoft_azure__ad_device": "true", "aws__emr_clusters": "true", "open_data__nvd_vulnerability": "true", "aws__emr_ec2_fleet": "true", "aws__sh_findings": "true", "cisa__vulnrichment": "true", "microsoft_azure__defender_device_list": "true", "rbac_project_lookup": "true", "aws__emr_ec2_instance": "true", "aws__resource_details": "true", "azure__aci_containers": "true", "microsoft_azure__defender_device_events": "true", "aws__ecs_task_containers": "true", "bamboohr__hr_report_pull": "true", "microsoft_azure__ad_registered_user": "true", "aws__ec2_describe_instances": "true", "aws__ecs_service_containers": "true", "microsoft_azure__file_share": "true", "microsoft_azure__defender_device_software_vuln": "true", "aws__identity_store_list_users": "true", "microsoft_azure__queue_storage": "true", "microsoft_azure__resource_list": "true", "microsoft_azure__table_storage": "true", "aws__identity_store_list_groups": "true", "aws__organization_list_accounts": "true", "microsoft_azure__ad_user_sign_in": "true", "microsoft_azure__defender_software": "true", "microsoft_azure__list_subscription": "true", "microsoft_azure__security_assesment": "true", "microsoft_azure__compliance_controls": "true", "microsoft_azure__ad_directory_members": "true", "microsoft_azure__compliance_standards": "true", "microsoft_azure__compliance_assessments": "true", "microsoft_azure__blob_storage_container": "true", "aws__list_aggregate_discovered_resources": "true", "aws__identity_store_list_group_memberships": "true", "microsoft_azure__virtual_machine": "true", "microsoft_azure__defender_tvm_secure_config": "true", "mega": "true", "microsoft__active_directory": "true", "aws_eks_container": "true", "azure_aks_container": "true"}