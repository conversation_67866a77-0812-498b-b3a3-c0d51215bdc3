{"origin": "'CrowdStrike Host'", "commonProperties": [{"colName": "last_active_date", "colExpr": "GREATEST(crowdstrike_last_report_date, crowdstrike_reboot_date, crowdstrike_modified_date,login_last_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "CASE WHEN LOWER(site_name) LIKE '%bangalore%' THEN 'India' WHEN LOWER(site_name) LIKE '%azusw%' OR LOWER(site_name) LIKE '%az-usw%' OR LOWER(site_name) LIKE '%azinds%' OR LOWER(site_name) LIKE '%tacoma%' THEN 'United States' ELSE NULL END"}, {"colName": "location_city", "colExpr": "CASE WHEN LOWER(site_name) LIKE '%bangalore%' THEN 'Bangalore' WHEN LOWER(site_name) LIKE '%tacoma%' THEN 'Tacoma' ELSE NULL END"}, {"colName": "display_label", "colExpr": "UPPER(coalesce(host_name,fqdn,dns_name,ip[0],primary_key))", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"srdm": "<%SRDM_SCHEMA_NAME%>.crowdstrike__host_list"}, "entitySpecificProperties": [{"colName": "login_last_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_login_timestamp)))"}, {"colName": "login_last_user", "colExpr": "last_login_user"}, {"colName": "fqdn", "colExpr": "UPPER(CASE WHEN host_name IS NOT NULL AND domain IS NOT NULL AND domain!='' AND domain NOT RLIKE '.*\\\\\\\\:.*' AND LOWER(host_name) != LOWER(domain) AND LOWER(domain) not rlike '^(workgroup|unknown|no domain)$' THEN CONCAT_WS('.', host_name, domain) ELSE NULL END)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colExpr": "case when device_policies.fim.applied=true then true else false end", "colName": "crowdstrike_fim_policy_status"}, {"colExpr": "case when device_policies.firewall.applied=true then true else false end ", "colName": "crowdstrike_firewall_status"}, {"colExpr": "CASE WHEN device_policies.sensor_update.uninstall_protection = 'ENABLED' and device_policies.sensor_update.applied = true THEN TRUE ELSE FALSE END", "colName": "crowdstrike_sensor_uninstall_protection_status"}, {"colExpr": "case when device_policies.prevention.applied=true then true else false end", "colName": "crowdstrike_prevention_policy_status"}, {"colExpr": "case when device_policies.sensor_update.applied=true then true else false end", "colName": "crowdstrike_sensor_update_policy_status"}, {"colExpr": "case when device_policies.remote_response.applied=true then true else false end", "colName": "crowdstrike_remote_response_status"}, {"colExpr": "case when device_policies.host__retention.applied=true then true else false end", "colName": "crowdstrike_host_retention_status"}, {"colExpr": "case when device_policies.global_config.applied=true then true else false end", "colName": "crowdstrike_global_config_status"}, {"colExpr": "case when device_policies.content__update.applied=true then true else false end", "colName": "crowdstrike_content_update_status"}, {"colExpr": "case when device_policies.device_control.applied=true then true else false end", "colName": "crowdstrike_device_control_status"}, {"colExpr": "CASE WHEN datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(crowdstrike_last_report_date/1000,'yyyy-MM-dd')) = -1 THEN 0 ELSE datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(crowdstrike_last_report_date/1000,'yyyy-MM-dd')) END", "colName": "crowdstrike_last_report_sla_duration", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN crowdstrike_last_report_sla_duration<=7 then 'True' else 'False' END", "colName": "crowdstrike_last_report_sla_status", "fieldsSpec": {"isInventoryDerived": true}}], "temporaryProperties": [{"colName": "event_timestamp_epoch", "colExpr": "GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(LaunchTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(array_max(NetworkInterfaces.Attachment.AttachTime)))),UNIX_MILLIS(TIMESTAMP(to_timestamp(UsageOperationUpdateTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(REGEXP_EXTRACT(StateTransitionReason, '([0-9]{4}-[0-9]{2}-[0-9]{2})')))),ingested_timestamp_epoch)"}, {"colName": "event_timestamp_ts", "colExpr": "to_timestamp(event_timestamp_epoch/1000)"}]}