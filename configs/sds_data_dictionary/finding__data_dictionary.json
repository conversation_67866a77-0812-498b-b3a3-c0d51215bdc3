{"caption": "Finding", "description": "A finding is a specific piece of information that indicates a potential security issue or anomaly within an organization's IT infrastructure, systems, applications, or processes.", "attributes": {"data_source_dev": {"caption": "Source of the Data", "description": "Specifies the origin whether the data is based on EDM or SIT.", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "dashboard_identifier": {"EI": {}, "Finding": {}}}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \\n\\nIf the time since the last inventory update is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \\nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 30 days.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "dashboard_identifier": {"EI": {}, "Finding": {}}}}}