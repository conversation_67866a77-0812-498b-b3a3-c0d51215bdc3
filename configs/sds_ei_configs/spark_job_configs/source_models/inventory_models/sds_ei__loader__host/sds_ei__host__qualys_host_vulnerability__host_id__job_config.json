{"temporaryProperties": [{"colName": "first_found_datetime_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(FIRST_FOUND_DATETIME)))"}], "entitySpecificProperties": [{"colName": "accessibility", "colExpr": "CASE WHEN LOWER(cast(qualys_tags as String)) LIKE '%internal%' THEN 'Internal' WHEN LOWER(cast(qualys_tags as String)) LIKE '%external%' THEN 'External' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,dns_name,cast(qualys_tags as String))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN LOWER(qualys_detection_method) LIKE '%agent%' AND vm_last_scan_date IS NOT NULL AND DATEDIFF(from_unixtime(updated_at / 1000, 'yyyy-MM-dd') , FROM_UNIXTIME(vm_last_scan_date / 1000, 'yyyy-MM-dd')) <= 30 THEN 'Onboarded' WHEN LOWER(qualys_detection_method) LIKE '%agent%' AND vm_last_scan_date IS NOT NULL AND DATEDIFF(CURRENT_DATE, FROM_UNIXTIME(vm_last_scan_date / 1000, 'yyyy-MM-dd')) > 30 THEN 'Was Onboarded' ELSE 'Not Onboarded' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "IP"}, {"colName": "host_name", "colExpr": "UPPER(REGEXP_EXTRACT(dns_name, '^([^.]+)'))", "fieldsSpec": {"isInventoryDerived": true}}], "primaryKey": "HOST_ID", "dataSource": {"srdm": "<%SRDM_SCHEMA_NAME%>.qualys__default__host_vulnerability"}}