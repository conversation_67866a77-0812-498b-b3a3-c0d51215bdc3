trigger_qa_ui_automation_pipeline:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON><PERSON> CR
    retries: 1
    start_date: 2024-01-01
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    enable_em_navigator_sanity_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [start_task]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/em_navigator_sanity_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    enable_em_sanity_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [start_task]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/em_sanity_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    trigger_em_navigator_sanity_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_em_navigator_sanity_dag]
      trigger_dag_id: "em_navigator_sanity_dag"
      wait_for_completion: true
    trigger_em_sanity_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_em_sanity_dag]
      trigger_dag_id: "em_sanity_dag"
      wait_for_completion: true
    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [trigger_em_navigator_sanity_dag, trigger_em_sanity_dag]
