trigger_platform_automation_pipeline:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON><PERSON> Kumar CR
    retries: 1
    start_date: 2024-01-01
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    enable_df_studio_feature_test_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [start_task]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/df_studio_feature_test_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    enable_platform_feature_tests_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [start_task]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/platform_feature_tests_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    enable_graphql_api_sanity_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [start_task]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/graphql_api_sanity_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    trigger_df_studio_feature_test_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_df_studio_feature_test_dag]
      trigger_dag_id: "df_studio_feature_test_dag"
      wait_for_completion: true
    trigger_platform_feature_tests_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_platform_feature_tests_dag]
      trigger_dag_id: "platform_feature_tests_dag"
      wait_for_completion: true
    trigger_graphql_api_sanity_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_graphql_api_sanity_dag]
      trigger_dag_id: "graphql_api_sanity_dag"
      wait_for_completion: true
    enable_graphql_api_regression_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [trigger_graphql_api_sanity_dag]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/graphql_api_regression_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    trigger_graphql_api_regression_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_graphql_api_regression_dag]
      trigger_dag_id: "graphql_api_regression_dag"
      wait_for_completion: true
    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [trigger_df_studio_feature_test_dag, trigger_graphql_api_regression_dag]