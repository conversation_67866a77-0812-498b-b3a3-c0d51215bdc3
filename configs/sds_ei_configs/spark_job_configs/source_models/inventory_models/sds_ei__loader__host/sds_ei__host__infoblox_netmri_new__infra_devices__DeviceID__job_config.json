{"dataSource": {"name": "Infoblox NetMRI New", "feedName": "Infra Devices", "srdm": "<%SRDM_SCHEMA_NAME%>.infoblox_netmri_new__infra_devices"}, "commonProperties": [{"colName": "display_label", "colExpr": "coalesce(DeviceDNSName, DeviceName,DeviceSysName, DeviceMAC,DeviceIPDotted)", "ds_recommended": true}, {"colName": "first_seen_date", "colExpr": "DeviceFirstOccurrenceTime", "ds_recommended": true}, {"colName": "last_active_date", "colExpr": "InfraDeviceTimestamp"}, {"colName": "location_city", "colExpr": "DeviceSysLocation"}], "entitySpecificProperties": [{"colName": "active_operational_date", "colExpr": "InfraDeviceTimestamp", "ds_recommended": true}, {"colName": "compliance_state", "colExpr": "CASE     WHEN DeviceAssurance >= 80 THEN 'Compliant'   ELSE 'Non-Compliant' END", "ds_recommended": true}, {"colName": "hardware_chassis_type", "colExpr": "CASE \n    WHEN LOWER(DeviceType) IN ('switch', 'firewall', 'router') THEN 'Server'\n    WHEN LOWER(DeviceType) = 'power controller' THEN 'Other'\n    WHEN LOWER(DeviceType) = 'netmri' THEN 'Desktop'\n    ELSE 'Invalid Chassis Type'\nEND", "ds_recommended": true}, {"colName": "hardware_manufacturer", "colExpr": "coalesce(DeviceOUI, DeviceVendor)", "ds_recommended": true}, {"colName": "hardware_model", "colExpr": "DeviceModel", "ds_recommended": true}, {"colName": "hardware_serial_number", "colExpr": "coalesce(DeviceUniqueKey, DeviceID, MgmtServerDeviceID)", "ds_recommended": true}, {"colName": "host_last_reboot_date", "colExpr": "to_timestamp(DeviceRebootTime, 'yyyy-MM-dd HH:mm:ss')", "ds_recommended": true}, {"colName": "host_name", "colExpr": " DeviceName", "ds_recommended": true}, {"colName": "ip", "colExpr": "array(DeviceIPDotted)", "ds_recommended": true}, {"colName": "mac_address", "colExpr": "array(DeviceMAC)", "ds_recommended": true}, {"colName": "dns_name", "colExpr": "DeviceDNSName"}, {"colName": "internal_contributor", "colExpr": "coalesce(DeviceType, DeviceName,DeviceSysDescr,DeviceVersion)"}, {"colName": "os_version", "colExpr": "DeviceVersion"}, {"colName": "region", "colExpr": "DeviceSysLocation"}], "sourceSpecificProperties": [], "primaryKey": "DeviceID", "origin": "'Infoblox NetMRI Infra Device'", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__infoblox_netmri_new__infra_devices__DeviceID", "entity": {}}