image:
  pullPolicy: IfNotPresent
imagePullSecrets:
  - name: docker-secret
backoffLimit: 1

activeDeadlineSeconds: 3600
ttlSecondsAfterFinished: 86400
restartPolicy: Never
secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
jobAnnotations: 
  argocd.argoproj.io/hook: Sync
  argocd.argoproj.io/hook-delete-policy: HookSucceeded
  argocd.argoproj.io/sync-options: Force=true,Replace=true

initImage:
  repository: prevalentai/devops-utils
  tag: kubectl1.27.0-awscliv2-azcpv10-azcli-curl-jq-bookworm-12.5-********-slim

jobLabels: {}

labelJob: sds-client-ibx-jobs

podLabels: 
  cronterminate: "false"


NAMESPACE: "{{ .Values.global.DEPLOY_NAMESPACE }}"
SDS3_DOMAIN: "{{ .Values.global.SDS3_DOMAIN_NAME }}"
mgmnt_api_svc_name:
jobEnv: {}

serviceAccount:
  create: false
  name: default

resources:
  limits:
    cpu: 2500m
    memory: 1000Mi
  requests:
    cpu: 1200m
    memory: 700Mi

bundleVersion:
  client: 6.5.1-********-***************

jobOverrides:
  image:
    repository: prevalentai/sds-client-ibx-configs
    tag:
  
  sds_nifi_shared_copy_job:
    enabled: false
    image:
      repository: prevalentai/sds-platform-apps-deployment-utils
      tag:
    command: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; mkdir -p /tmp/artifact_location/ && python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-client-ibx --sol_name sds-client-ibx --comp_name sds-client-ibx --obj_store_path file:///tmp/artifact_location --artifact_identifier sds-client-ibx-nifi-python --artifact_name nifi-python-files.tar.gz && tar -xzvf /tmp/artifact_location/nifi-python-files.tar.gz -C /opt/nifi/nifi-scripts/ && rm -rf /opt/nifi/nifi-scripts/venv && python -m venv /opt/nifi/nifi-scripts/venv && cd /opt/nifi/nifi-scripts/ && ./venv/bin/python -m ensurepip --upgrade && ./venv/bin/python -m pip install -r requirements.txt; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
    env:
      common:
        - name: ARTIFACT_URL
          value: https://prevalentai.jfrog.io/
      aws: []
      azure: []
    extraEnv: []

  sds_airflow_variable_update_job:
    enabled: true
    command: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; python /opt/airflow/pe_configs/scripts/config_template_renderer.py /opt/airflow/final_configs/config_context/{{ .Values.global.CONFIG_CONTEXT }} /opt/airflow/final_configs/orchestration_variables && airflow db init && for var_files in `ls /opt/airflow/final_configs/orchestration_variables/`; do airflow variables import /opt/airflow/final_configs/orchestration_variables/$var_files; done; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
    env:
      common: []
      aws: []
      azure: []
    extraEnv: []
    volumeMounts: []
    volumes: []


  sds_airflow_shared_copy_job:
    enabled: true
    command: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; python /opt/airflow/pe_configs/scripts/config_template_renderer.py /opt/airflow/final_configs/config_context/{{ .Values.global.CONFIG_CONTEXT }} /opt/airflow/final_configs/orchestration_shared_fs && cp -rf /opt/airflow/final_configs/orchestration_shared_fs/* /opt/airflow/shared/; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
    env:
      common: []
      aws: []
      azure: []
    extraEnv: []

  em_api_config_deploy_job:
    enabled: true
    image:
      repository: prevalentai/sds-client-ibx-configs
      tag:
      pullPolicy: IfNotPresent    
    command: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; python /opt/airflow/em_configs/scripts/em_api_config_deploy.py --em_api_final_config_path=/opt/airflow/em_final_configs/api_configs/ --deployment_config_path=/opt/airflow/em_final_configs/deployment_config.json --config_context_folder_path=/opt/airflow/final_configs/config_context run; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
    env:
      common:
        - name: AUTH_TOKEN_URL
          value: https://{{ .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ .Values.global.DEPLOY_NAMESPACE }}/protocol/openid-connect/token
        - name: CONFIG_API_BASE_URL
          value: https://{{ .Values.global.SDS3_DOMAIN_NAME }}/
      aws: []
      azure: []
    extraEnv: []

  ei_config_client_deploy_job:
    enabled: true
    initJob:
      image:
        repository: prevalentai/sds-client-ibx-configs
        tag:

    mainContainer:
      image:
        repository: prevalentai/sds-platform-apps-deployment-utils
        tag:
      ei_version:
      em_version:
      env_type:

    image:
      repository: prevalentai/sds-client-ibx-configs
    autoscaling:
      enabled: true

    initCommand: ["bash", "-c", "until nc -vz {{ index .Values \"mgmnt_api_svc_name\" | default \"mgmtapis\" }}.{{ tpl .Values.NAMESPACE . }}.svc.cluster.local 8080; do echo \"Waiting for management api service\"; sleep 2; done; cp -r /opt/airflow/ /tmp/"]
    command: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; current_time=$(date -u +\"%Y-%m-%dT%H:%M:%SZ\") && 
              echo -e \"Deploying EI config types\" ; 
              python /opt/sds-pe-deployment-utils/config_manager/config_item_type_upload_script.py --base_path=$EI_CONFIG_TYPE_BASE_PATH --environ_type=$ENV_TYPE --solution=ei --version=$EI_VERSION run && 
              echo -e \"Deploying EM config types\" ; 
              python /opt/sds-pe-deployment-utils/config_manager/config_item_type_upload_script.py --base_path=$EM_CONFIG_TYPE_BASE_PATH --environ_type=$ENV_TYPE --solution=em --version=$EM_VERSION run && 
              echo -e \"Deploying platform configs configs\" && 
              python /opt/sds-pe-deployment-utils/config_manager/sds_studio_config_load.py --base_path=$BASE_PATH --config_item_level=platform --repo_name=$PLATFORM_REPO_NAME --environ_type=$ENV_TYPE --version=$PE_VERSION --platform_version=$PE_VERSION --current_time=$current_time --solution_name=ei run && 
              echo -e \"Deploying EI solution configs\" &&
              python /opt/sds-pe-deployment-utils/config_manager/sds_studio_config_load.py --base_path=$BASE_PATH --config_item_level=solution --repo_name=$SOLUTION_REPO_NAME --environ_type=$ENV_TYPE --version=$EI_VERSION --solution_name=ei --current_time=$current_time --opt_upgrade_mode=false  run &&
              echo -e \"EI Solution configs deployed\" &&
              echo -e \"Deploying EM solution configs\" &&
              python /opt/sds-pe-deployment-utils/config_manager/sds_studio_config_load.py --base_path=$BASE_PATH --config_item_level=solution --repo_name=$EM_REPO_NAME --environ_type=$ENV_TYPE --version=$EM_VERSION --solution_name=em --current_time=$current_time --opt_upgrade_mode=false run &&
              echo -e \"EM Solution configs deployed\" &&
              echo -e \"Deploying EI client configs\" &&
              python /opt/sds-pe-deployment-utils/config_manager/sds_studio_config_load.py --base_path=$BASE_PATH --config_item_level=client --repo_name=$CLIENT_REPO_NAME --version=$EI_VERSION --solution_name=ei --platform_version=$PE_VERSION --environ_type=$ENV_TYPE --current_time=$current_time run &&
              echo -e \"EI Client configs deployed\" &&
              echo -e \"Deploying EM Client configs deployed\" &&
              python /opt/sds-pe-deployment-utils/config_manager/sds_studio_config_load.py --base_path=$BASE_PATH --config_item_level=client --repo_name=$EM_CLIENT_REPO_NAME --version=$EM_VERSION --solution_name=em --platform_version=$PE_VERSION --environ_type=$ENV_TYPE --current_time=$current_time run &&
              echo -e \"EM Client configs deployed\" &&
              echo -e \"Running EI config patch script\" &&
              python /opt/sds-pe-deployment-utils/config_manager/config_patch.py --base_path=$BASE_PATH --repo_name=$CLIENT_REPO_NAME --sol_repo_name=$SOLUTION_REPO_NAME --environ_type=$ENV_TYPE --config_item_level=client --current_time=$current_time --solution_version=$EI_VERSION --solution=ei run &&
              echo -e \"Finished EI config patch script\" &&
              echo -e \"Running EM config patch script\" &&
              python /opt/sds-pe-deployment-utils/config_manager/config_patch.py --base_path=$BASE_PATH --repo_name=$EM_CLIENT_REPO_NAME --sol_repo_name=$EM_REPO_NAME --environ_type=$ENV_TYPE --config_item_level=client --current_time=$current_time --solution_version=$EM_VERSION --solution=em run &&
              echo -e \"Finished EM config patch script\" &&
              echo -e \"Running config refresh script\" &&
              python /opt/sds-pe-deployment-utils/config_manager/config_refresh.py --environ_type=$ENV_TYPE --current_time=$current_time --solution_version=$EM_VERSION run &&
              echo -e \"Deploying platform configs configs\" &&
              python /opt/sds-pe-deployment-utils/config_manager/sds_studio_config_load.py --base_path=$BASE_PATH --config_item_level=platform --repo_name=$PLATFORM_REPO_NAME --environ_type=$ENV_TYPE --version=$PE_VERSION --solution_name=ei --current_time=$current_time run &&
              echo \"Config patch script ran\"; x=$(echo $?);
              curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]

    env:
      common:
        - name: BASE_PATH
          value: /tmp/airflow/
        - name: PLATFORM_REPO_NAME
          value: pe_configs
        - name: SOLUTION_REPO_NAME
          value: ei_configs        
        - name: CLIENT_REPO_NAME
          value: sds_ei_configs_temp
        - name: EM_REPO_NAME
          value: em_configs
        - name: EM_CLIENT_REPO_NAME
          value: sds_em_configs
        - name: GRANT_TYPE
          value: client_credentials
        - name: CONFIG_ITEM_LEVEL
          value: client
        - name: EI_CONFIG_TYPE_BASE_PATH
          value: /tmp/airflow/ei_configs
        - name: EM_CONFIG_TYPE_BASE_PATH
          value: /tmp/airflow/em_configs
      aws: []
      azure: []
    extraEnv: []


  spark_config_copy_job:
    enabled: true
    command:
      aws: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; python /opt/airflow/pe_configs/scripts/config_template_renderer.py /opt/airflow/final_configs/config_context/{{ .Values.global.CONFIG_CONTEXT }} /opt/airflow/final_configs/spark_job_configs && aws s3 cp /opt/airflow/final_configs/spark_job_configs s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-ei-configs --recursive && if [ -d '/opt/airflow/final_configs/spark_job_configs/sds_autoparser_configs' ]; then aws s3 cp /opt/airflow/final_configs/spark_job_configs/sds_autoparser_configs/ s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-autoparser-configs/ --recursive; fi; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"] 
      azure: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\";
              python /opt/airflow/pe_configs/scripts/config_template_renderer.py /opt/airflow/final_configs/config_context/{{ .Values.global.CONFIG_CONTEXT }} /opt/airflow/final_configs/spark_job_configs &&
              az login --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --output none --only-show-errors &&
              echo \"Copying sds-ei-configs...\" &&
              az storage blob upload-batch --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --destination {{ .Values.global.BLOB_APPS_CONTAINER_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-ei-configs --source \"/opt/airflow/final_configs/spark_job_configs\" --auth-mode login --output none --only-show-errors &&
              if [ -d '/opt/airflow/final_configs/spark_job_configs/sds_autoparser_configs' ]; then
                echo \"Copying sds-autoparser-configs...\" &&
                az storage blob upload-batch --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --destination {{ .Values.global.BLOB_APPS_CONTAINER_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-autoparser-configs --source \"/opt/airflow/final_configs/spark_job_configs/sds_autoparser_configs\" --auth-mode login --output none --only-show-errors;
              fi;
              x=$(echo $?);
              curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]

    env:
      common: []
      aws: []
      azure: []
    extraEnv: []

  sds_client_dag_deploy_job:
    enabled: true
    backoffLimit: 1
    image:
      repository: prevalentai/sds-client-ibx-orchestration
      tag:
      pullPolicy: IfNotPresent
    command:
      aws: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; /tmp/aws/bin/aws s3 cp /opt/airflow/sds/dags/{{ .Values.global.CLIENT_CODE }}.tgz s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/dags/ && /tmp/aws/bin/aws s3 cp /opt/airflow/sds/commons/{{ .Values.global.CLIENT_CODE }}.tgz s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/commons/ && /tmp/aws/bin/aws s3 cp /opt/airflow/sds/plugins/{{ .Values.global.CLIENT_CODE }}.tgz s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/plugins/ && kubectl rollout restart deployment --selector=component=scheduler -n {{ .Values.global.DEPLOY_NAMESPACE }}; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
      azure: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\";
              az login --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --output none --only-show-errors &&
              echo \"Copying Dags...\" &&
              az storage blob upload --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name \"{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/dags/{{ .Values.global.CLIENT_CODE }}.tgz\" --file \"/opt/airflow/sds/dags/{{ .Values.global.CLIENT_CODE }}.tgz\" --auth-mode login --output none --only-show-errors &&
              echo \"Copying Plugins...\" &&
              az storage blob upload --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name \"{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/plugins/{{ .Values.global.CLIENT_CODE }}.tgz\" --file \"/opt/airflow/sds/plugins/{{ .Values.global.CLIENT_CODE }}.tgz\" --auth-mode login --output none --only-show-errors &&
              echo \"Copying Commons...\" &&
              az storage blob upload --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name \"{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/commons/{{ .Values.global.CLIENT_CODE }}.tgz\" --file \"/opt/airflow/sds/commons/{{ .Values.global.CLIENT_CODE }}.tgz\" --auth-mode login --output none --only-show-errors &&
              kubectl rollout restart deployment --selector=component=scheduler -n {{ .Values.global.DEPLOY_NAMESPACE }}; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]

      limits:
        cpu: 100m
        memory: 2500Mi
      requests:
        cpu: 50m
        memory: 1500Mi
    env:
      common: []
      aws: []
      azure: []
    extraEnv: [] 

