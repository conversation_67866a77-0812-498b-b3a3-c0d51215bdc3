apiVersion: v1
kind: ConfigMap
metadata:
  name: dependencies-configmap-{{ .Release.Name }}
data:
  dependencies.yaml: |
    "sds-client-ibx":
      bundle-version: {{ .Values.bundleVersion.client | quote }}
      component-override:
        {{- $bundleVersion := index .Values "bundle-version" }}
        {{- if index .Values "jobOverrides" "sds_nifi_shared_copy_job" "bundle-version" }}
        "sds-client-ibx": {{ upper (index  .Values "jobOverrides" "sds_nifi_shared_copy_job" "bundle-version") }}
        {{- else }}        
        "sds-client-ibx" : {{ if $bundleVersion }}{{ upper $bundleVersion }}{{ else }}""{{ end }}
        {{- end }}        
  job-overrides: |
    {{ tpl (toYaml .Values.jobOverrides) . | nindent 6 }}

