{{/*
Expand the name of the chart.
*/}}
{{- define "k8jobs.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "k8jobs.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "k8jobs.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "k8jobs.labels" -}}
helm.sh/chart: {{ include "k8jobs.chart" . }}
{{ include "k8jobs.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "k8jobs.selectorLabels" -}}
app.kubernetes.io/name: {{ include "k8jobs.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "k8jobs.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "k8jobs.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{- define "job_environment_from" }}
  {{- $Global := . }}
  {{- with .Values.jobEnvFrom }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "job_environment" }}
  {{- $Global := . }}
  {{- with .Values.jobEnv }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes_mounts" }}
  {{- $Global := . }}
  {{- with .Values.volumeMounts }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes" }}
  {{- $Global := . }}
  {{- with .Values.volumes }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}
