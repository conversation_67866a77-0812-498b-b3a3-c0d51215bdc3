{{- if .Values.jobOverrides.sds_client_dag_deploy_job.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: sds-client-dag-deploy-job
  labels:
    job: {{ .Values.labelJob }}
    app: sds-client-dag-deploy-job
spec:
  backoffLimit: {{ default .Values.backoffLimit .Values.jobOverrides.sds_client_dag_deploy_job.backoffLimit }}
  activeDeadlineSeconds: {{ default .Values.activeDeadlineSeconds .Values.jobOverrides.sds_client_dag_deploy_job.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ default .Values.ttlSecondsAfterFinished .Values.jobOverrides.sds_client_dag_deploy_job.ttlSecondsAfterFinished }}
  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        app: sds-client-dag-deploy-job
        {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
        azure.workload.identity/use: "true"
        {{ end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: dag-deploy-job-sa
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ (index .Values "jobOverrides" "sds_client_dag_deploy_job" "image" "repository") }}:{{ include "dagdeploy.getImageTag" . }}"
          imagePullPolicy: {{ (index .Values "jobOverrides" "sds_client_dag_deploy_job" "image" "pullPolicy") }}
          {{- with index .Values "jobOverrides" "sds_client_dag_deploy_job" }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
          command: {{ tpl (toYaml .command.aws) $ | nindent 12 }}
          {{- end }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
          command: {{ tpl (toYaml .command.azure) $ | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- if .Values.args }}
          args: {{ tpl (toYaml .Values.args) . | nindent 12 }}
          {{- end }}
          env: 
            {{- include "custom_client_dag_deploy_job_environment" . | indent 10 }}
          resources:
            {{- toYaml (index .Values "jobOverrides" "sds_client_dag_deploy_job" "resources") | nindent 12 }}
          volumeMounts: []
      volumes: []
{{- end }}      