trigger_data_pipeline_new:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON><PERSON> CR
    retries: 1
    start_date: 2024-01-01
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    enable_all_dependant_dag:
      operator: airflow.operators.bash.BashOperator
      dependencies: [start_task]
      bash_command: >
       curl -s -X GET -k "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/variables/SDS_ORCHESTRATOR_CONFIG" 
       -H "Content-Type: application/json" -u "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}:{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}"
       |python -c 'import sys, json; data = json.loads(json.loads(sys.stdin.read())["value"]); 
       values = set(d for dep in data["SDS_EI_PIPELINE"]["dependencies"] for d in dep["dependencies"]); 
       values.remove("sds_ei_start"); print("\n".join(values))'| 
       while read value; 
       do curl -X PATCH -k  "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/dags/$value" 
       -H "Content-Type: application/json" -u "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}:{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}"
       -d '{"is_paused": false}'; 
       done
    check_dependent_dags_status:
      operator: airflow.operators.bash.BashOperator
      dependencies: [enable_all_dependant_dag]
      retries: 30
      retry_delay: 300
      bash_command: >
        dag_ids=$(curl -s -X GET -k "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/variables/SDS_ORCHESTRATOR_CONFIG" -H "Content-Type: application/json" -u "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}:{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}" | python3 -c 'import sys, json; data = json.loads(json.loads(sys.stdin.read())["value"]); dag_ids = set(d for dep in data["SDS_EI_PIPELINE"]["dependencies"] for d in dep["dependencies"]); dag_ids.discard("sds_ei_start"); print("\n".join(dag_ids))') && all_ok=true && while read dag_id; do echo "Checking DAG: $dag_id"; response=$(curl -s -X GET -k "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/dags/$dag_id/dagRuns" -H "Content-Type: application/json" -u "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}:{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}"); dag_runs=$(echo "$response" | python3 -c 'import sys, json; print(json.dumps(json.loads(sys.stdin.read())["dag_runs"]))'); run_count=$(echo "$dag_runs" | python3 -c 'import sys, json; print(len(json.loads(sys.stdin.read())))'); has_failed=$(echo "$dag_runs" | python3 -c 'import sys, json; print(any(run["state"] == "failed" for run in json.loads(sys.stdin.read())))'); [[ "$has_failed" == "True" ]] && echo "DAG $dag_id has a failed run. Exiting successfully to avoid retry." && exit 0; [[ "$run_count" -lt 2 ]] && echo "DAG $dag_id has less than 2 runs. Failing task to trigger retry." && all_ok=false && break; done <<< "$dag_ids"; $all_ok && echo "All DAGs have at least 2 runs and none have failed. Task passes." || (echo "At least one DAG has less than 2 runs. Task fails to trigger retry." && exit 1)  
    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [check_dependent_dags_status]