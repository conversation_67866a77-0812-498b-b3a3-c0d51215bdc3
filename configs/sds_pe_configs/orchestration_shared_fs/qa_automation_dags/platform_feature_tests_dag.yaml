platform_feature_tests_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON>
    retries: 1
    start_date: 2024-01-01
  task_groups:
    Platform_DevOps_tests:
      tooltip: "Grouped DevOps feature tests"
  tasks:
    start_task:
      operator: airflow.operators.empty.EmptyOperator
    enable_platform_feature_tests_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [start_task]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/trigger_autoparser_dags'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
      task_group_name: Platform_DevOps_tests
    parser_test:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [start_task]
      trigger_dag_id: "trigger_autoparser_dags"
      wait_for_completion: true
      task_group_name: Platform_DevOps_tests
      trigger_rule: "all_success"
    end_task:
      dependencies:
      - parser_test
      operator: airflow.operators.empty.EmptyOperator
