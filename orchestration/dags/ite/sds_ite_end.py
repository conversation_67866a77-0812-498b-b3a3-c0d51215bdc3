
from datetime import datetime

from airflow.operators.dummy import DummyOperator
from commons.ite.constants import IteConstants
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG

with SDSPipelineDAG(
    pipeline_id=IteConstants.MODULE_NAME,
    dag_id="sds_ite_end",
    tags=[IteConstants.MODULE_NAME, "QA_AUTOMATION"],
    start_date=datetime(2000, 1, 1),
    max_active_runs=1,
    concurrency=1,
    catchup=False,
) as ite_end_dag:
    ite_end_pipeline = DummyOperator(task_id="end_pipeline")