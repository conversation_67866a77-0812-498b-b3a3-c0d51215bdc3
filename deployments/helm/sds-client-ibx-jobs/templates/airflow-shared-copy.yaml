{{- if .Values.jobOverrides.sds_airflow_shared_copy_job.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    job: {{ .Values.labelJob }}
    {{- include "k8jobs.labels" . | nindent 4 }}
  name: airflow-shared-integration-{{ .Release.Name}}
  namespace: {{ .Release.Namespace }}
spec:
  backoffLimit: {{ default .Values.backoffLimit .Values.jobOverrides.sds_airflow_shared_copy_job.backoffLimit }}
  activeDeadlineSeconds: {{ default .Values.activeDeadlineSeconds .Values.jobOverrides.sds_airflow_shared_copy_job.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ default .Values.ttlSecondsAfterFinished .Values.jobOverrides.sds_airflow_shared_copy_job.ttlSecondsAfterFinished }}
  template:
    metadata:
      metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "k8jobs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - image: "{{ (index .Values "jobOverrides" "image" "repository") }}:{{ include "airflowsharedcopy.getImageTag" . }}"
          imagePullPolicy: IfNotPresent
          name: airflow
          resources: {}  
          {{- with index .Values "jobOverrides" "sds_airflow_shared_copy_job" }}
          command: {{ tpl (toYaml .command) $ | nindent 12 }}
          {{- end }}                          
          env: 
          {{- include "custom_airflow_shared_copy_job_environment" . | indent 10 }}
          volumeMounts:
            - mountPath: /opt/airflow/shared
              name: shared
      volumes:
        - name: shared
          persistentVolumeClaim:
            claimName: airflow-shared

{{- end }}