dag_status_check_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON><PERSON> CR
    retries: 1
    start_date: 2024-01-01
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    wait_for_dag_runs:
      operator: airflow.operators.bash.BashOperator
      dependencies: [start_task]
      bash_command: >
        python -c 'import time, requests, sys; MAX_RETRIES=20; RETRY_INTERVAL=30; 
        AIRFLOW_API_URL="{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/dags/your_dag_id/dagRuns?order_by=-execution_date&limit=5"; 
        auth=("{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}", "{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}"); 
        retry = 0; 
        while retry < MAX_RETRIES: 
            response = requests.get(AIRFLOW_API_URL, verify=False, headers={"Content-Type": "application/json"}, auth=auth); 
            success_count = sum(1 for run in response.json().get("dag_runs", []) if run["state"] == "success") if response.status_code == 200 else 0; 
            if success_count >= 2: print(f"✅ Found {success_count} successful DAG runs, proceeding..."); sys.exit(0); 
            print(f"⏳ Waiting for at least 2 successful DAG runs, found {success_count}..."); 
            time.sleep(RETRY_INTERVAL); retry += 1; 
        print("❌ Timeout reached, DAG runs did not complete in time."); sys.exit(1);'

    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [wait_for_dag_runs]