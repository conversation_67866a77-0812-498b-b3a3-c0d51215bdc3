{"id": "END-01-02", "title": "Devices have fully functional endpoint protection agent", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 6, "is_active": true, "description": "This assessment verifies whether endpoint devices have a fully functional endpoint protection agent. This ensures active protection against threats and unauthorized activity on managed assets.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'EDR Fully Functional' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,edr_fully_functional,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "edr_fully_functional='True'", "finding_primary_key": "p_id", "finding_title": "EDR not fully functional", "finding_config": [{"title": "EDR Fully Functional", "expression": "edr_fully_functional", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-01"]}}