{"id": "VPM-02-11", "title": "Devices do not have remote access tool-related vulnerabilities", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment checks for the presence of vulnerabilities related to known remote access tools like TeamViewer, AnyDesk, and VNC. These tools, if exploited or misused, can provide adversaries with direct access to internal systems.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Device from Host", "Use 'Host Has Vulnerability Finding' relationship from Graph filter", "Validate that 'Software Product' not like teamviewer,anydesk,vnc"], "scope_query": "SELECT host.p_id AS host_p_id, ARRAY(STRUCT(host.p_id, host.class, host.display_label, host.activity_status)) AS affected_asset, host.origin AS affected_asset_origin,collect_set(reln.software_product) as software_product,collect_set(CASE WHEN reln.current_status = 'Open' THEN reln.current_status ELSE NULL END) AS vuln_status FROM <%EI_SCHEMA_NAME%>.sds_ei__host__enrich AS host LEFT JOIN <%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host__enrich AS reln ON host.p_id = reln.target_p_id LEFT JOIN (select title, p_id, description from <%EI_SCHEMA_NAME%>.sds_ei__vulnerability__enrich) AS vuln ON reln.source_p_id = vuln.p_id WHERE host.activity_status = 'Active' AND (host.type = 'Server' OR host.type = 'Workstation') GROUP BY host.p_id, host.class, host.display_label, host.activity_status, host.origin", "success_condition": "CASE WHEN LOWER(ARRAY_JOIN(software_product, ',')) LIKE '%teamviewer%' OR LOWER(ARRAY_JOIN(software_product, ',')) LIKE '%anydesk%' OR LOWER(ARRAY_JOIN(software_product, ',')) LIKE '%vnc%' THEN false ELSE true END", "finding_primary_key": "host_p_id", "finding_title": "Remote Access Tool Vulnerability Detected", "finding_config": [{"title": "Software Product", "expression": "software_product", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["VPM-02"], "PCI DSS v4.0.1": []}}