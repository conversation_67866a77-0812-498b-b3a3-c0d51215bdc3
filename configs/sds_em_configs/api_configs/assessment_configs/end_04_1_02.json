{"id": "END-04-1-02", "title": "Devices with EDR agents have content update enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 7, "is_active": true, "description": "This assessment verifies whether Content Update is enabled on devices that have been onboarded to the EDR agent. Enabling Content Update ensures the latest detection rules, signatures, and configurations are applied. Devices without this setting may lack protection against the latest threats and exhibit reduced detection capabilities.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'CrowdStrike Content Update Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_content_update_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_content_update_status=true", "finding_primary_key": "p_id", "finding_title": "Crowdstrike Content Update Not Enabled", "finding_config": [{"title": "CrowdStrike Content Update Status", "expression": "crowdstrike_content_update_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-04.1"]}}