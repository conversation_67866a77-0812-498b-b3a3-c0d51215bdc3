{"id": "END-06.2-02", "title": "Devices with EDR agents have remote response enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment verifies whether Remote Response is enabled on devices that have been onboarded to an EDR agent. Remote Response allows security teams to remotely investigate, contain, and remediate threats on endpoints. Devices without this capability may delay incident response and reduce investigation effectiveness.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'CrowdSsrike Remote Response Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_remote_response_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_remote_response_status=true", "finding_primary_key": "p_id", "finding_title": "Crowdstrike Remote Response Not Enabled", "finding_config": [{"title": "CrowdSsrike Remote Response Status", "expression": "crowdstrike_remote_response_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-06.2"]}}