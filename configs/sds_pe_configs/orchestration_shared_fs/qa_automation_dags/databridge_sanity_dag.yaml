databridge_sanity_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: Athira_Damodaran
    retries: 1
    start_date: 2024-01-01
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: prevalentai/sds-solution-studio-qa-automation:<%STUDIO_QA_IMAGE_VERSION%>
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      report_base_path: <%QA_REPORT_URI%>/behave
      run_prerequisites: false
      generate_report: true
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true      
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        TEST_ENV: "<%KUBE_NAMESPACE%>"
        EXEC_ENVIRONMENT: "<%EXEC_ENVIRONMENT%>"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        DOMAIN_URL: "{{macros.custom_macros.get_env('DOMAIN_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        DATALAKE_URI: "<%DATALAKE_URI%>"
        KEYCLOAK_SSL_VERIFY: "{{macros.custom_macros.get_env('KEYCLOAK_SSL_VERIFY', True)}}"
        DATALAKE_LOG_URI: "<%LOG_URI%>"
        TENANT_ID: <%msitenant%>
        AZURE_MANAGED_IDENTITY_ID: <%clientid%> 
        REQUESTS_CA_BUNDLE: /mnt/CABundle/pythoncacerts
      volume_mounts:
      - mount_path: /mnt/CABundle
        sub_path: null
        name: pythoncacerts
        read_only: true
      volumes:
      - name: pythoncacerts
        configs:
          secret:
            defaultMode: 511
            secretName: pythoncacerts
      secrets:
      - deploy_type: env
        deploy_target: SELENIUM_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumUsername
      - deploy_type: env
        deploy_target: SELENIUM_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumPassword
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: AIRFLOW_API_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiUsername
      - deploy_type: env
        deploy_target: AIRFLOW_API_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
  tasks:
    start_task:
      operator: airflow.operators.empty.EmptyOperator
    run_databridge_sanity:
      dependencies:
      - start_task
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      feature_files:
      - src/studio_ui/features/studio_connectors.feature
    end_task:
      dependencies:
      - run_databridge_sanity
      operator: airflow.operators.empty.EmptyOperator
      trigger_rule: "all_done"
