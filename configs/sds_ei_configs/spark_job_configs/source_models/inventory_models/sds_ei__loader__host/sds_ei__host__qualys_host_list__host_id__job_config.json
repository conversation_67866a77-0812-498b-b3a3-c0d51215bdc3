{"commonProperties": [{"colName": "last_active_date", "colExpr": "GREATEST(vm_last_scan_date, CAST(host_last_reboot_date AS LONG))", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "vm_onboarding_status", "colExpr": "CASE WHEN LOWER(qualys_detection_method) LIKE '%agent%' AND vm_last_scan_date IS NOT NULL AND DATEDIFF(from_unixtime(updated_at / 1000, 'yyyy-MM-dd') , FROM_UNIXTIME(vm_last_scan_date / 1000, 'yyyy-MM-dd')) <= 30 THEN 'Onboarded' WHEN LOWER(qualys_detection_method) LIKE '%agent%' AND vm_last_scan_date IS NOT NULL AND DATEDIFF(CURRENT_DATE, FROM_UNIXTIME(vm_last_scan_date / 1000, 'yyyy-MM-dd')) > 30 THEN 'Was Onboarded' ELSE 'Not Onboarded' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "UNIX_MILLIS( TIMESTAMP( TO_TIMESTAMP( GREATEST( CASE WHEN LAST_VM_SCANNED_DATE NOT IN ('[]', '(never)', 'NULL') THEN LAST_VM_SCANNED_DATE END, CASE WHEN LAST_VULN_SCAN_DATETIME NOT IN ('[]', '(never)', 'NULL') THEN LAST_VULN_SCAN_DATETIME END, CASE WHEN LAST_VM_AUTH_SCANNED_DATE NOT IN ('[]', '(never)', 'NULL') THEN LAST_VM_AUTH_SCANNED_DATE END, CASE WHEN LAST_COMPLIANCE_SCAN_DATETIME NOT IN ('[]', '(never)', 'NULL') THEN LAST_COMPLIANCE_SCAN_DATETIME END, CASE WHEN LAST_SCAP_SCAN_DATETIME NOT IN ('[]', '(never)', 'NULL') THEN LAST_SCAP_SCAN_DATETIME END, CASE WHEN LAST_ACTIVITY NOT IN ('[]', '(never)', 'NULL') THEN LAST_ACTIVITY END ))))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "host_last_reboot_date", "colExpr": "UNIX_MILLIS( TIMESTAMP( TO_TIMESTAMP(LAST_BOOT)))"}], "dataSource": {"srdm": "<%SRDM_SCHEMA_NAME%>.qualys__default__host_list"}}