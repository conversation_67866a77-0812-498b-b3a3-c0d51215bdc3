ARG SDS_PLATFORM_APPS
ARG SDS_SOLUTION_EI
ARG SDS_PRODUCT_EM
ARG SDS_SOLUTION_STUDIO



ARG PE_CONFIG_BASE_IMAGE="prevalentai/sds-platform-apps-configs:"
ARG EI_CONFIG_BASE_IMAGE="prevalentai/sds-solution-ei-configs:"
ARG EM_CONFIG_BASE_IMAGE="prevalentai/sds-product-em-configs:"
ARG STUDIO_CONFIG_BASE_IMAGE="prevalentai/sds-solution-studio-configs:"


FROM ${STUDIO_CONFIG_BASE_IMAGE}${SDS_SOLUTION_STUDIO} AS studioconf

FROM ${EM_CONFIG_BASE_IMAGE}${SDS_PRODUCT_EM} AS emconf

FROM ${EI_CONFIG_BASE_IMAGE}${SDS_SOLUTION_EI} AS eiconf

FROM ${PE_CONFIG_BASE_IMAGE}${SDS_PLATFORM_APPS} AS peconf



COPY --from=eiconf /opt/airflow/ei_configs /opt/airflow/ei_configs
COPY --from=emconf /opt/airflow/em_configs/ /opt/airflow/em_configs/
COPY --from=studioconf /opt/airflow/studio_configs/ /opt/airflow/studio_configs/

COPY  ./sds_ei_configs /opt/airflow/sds_ei_configs
COPY  ./sds_ei_configs /opt/airflow/sds_ei_configs_temp
COPY  ./sds_em_configs /opt/airflow/sds_em_configs
COPY  ./sds_pe_configs /opt/airflow/sds_pe_configs
COPY  ./scripts /opt/airflow/scripts/
COPY  ./sds_data_dictionary /opt/airflow/sds_ei_configs_temp/sds_data_dictionary
COPY  ./sds_relationship_data_dictionary /opt/airflow/sds_ei_configs_temp/sds_relationship_data_dictionary


USER root
RUN chown -R airflow:0 /opt/airflow/ \
&& ln -s /usr/local/bin/python /bin/python
USER airflow
RUN python -u /opt/airflow/sds_ei_configs/scripts/merge_orchestration_vars.py /opt/airflow/sds_ei_configs/ 
RUN python -u /opt/airflow/sds_ei_configs/scripts/merge_orchestration_vars.py /opt/airflow/sds_ei_configs_temp/
RUN python /opt/airflow/pe_configs/scripts/platform_config_merge.py /opt/airflow/pe_configs \
/opt/airflow/sds_pe_configs /opt/airflow/pe_final_configs true
RUN python /opt/airflow/pe_configs/scripts/platform_config_merge.py /opt/airflow/ei_configs \
/opt/airflow/sds_ei_configs /opt/airflow/ei_final_configs true 
RUN python /opt/airflow/pe_configs/scripts/platform_config_merge.py /opt/airflow/em_configs \
/opt/airflow/sds_em_configs /opt/airflow/em_final_configs true
RUN python /opt/airflow/pe_configs/scripts/platform_config_merge.py /opt/airflow/pe_final_configs \
/opt/airflow/ei_final_configs /opt/airflow/pe_ei_final_configs true 
RUN python /opt/airflow/pe_configs/scripts/platform_config_merge.py /opt/airflow/pe_ei_final_configs \
/opt/airflow/em_final_configs /opt/airflow/pe_ei_em_final_configs true
RUN python /opt/airflow/pe_configs/scripts/platform_config_merge.py /opt/airflow/pe_ei_em_final_configs \
/opt/airflow/studio_configs /opt/airflow/final_configs true


RUN python /opt/airflow/ei_configs/scripts/platform_merger_extended.py /opt/airflow/ei_configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary \
    /opt/airflow/sds_data_dictionary /opt/airflow/ei_dict_configs 
RUN python /opt/airflow/ei_configs/scripts/platform_merger_extended.py /opt/airflow/ei_configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_relationship_data_dictionary \
    /opt/airflow/sds_relationship_data_dictionary /opt/airflow/ei_rel_configs 
RUN python /opt/airflow/ei_configs/scripts/dictionary_combine.py --input_folder /opt/airflow/ei_dict_configs    --output_filename /opt/airflow/final_configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_ei_reference_dd.json 
RUN python /opt/airflow/ei_configs/scripts/dictionary_combine.py --input_folder /opt/airflow/ei_rel_configs    --output_filename /opt/airflow/final_configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_ei_reference_relation_dd.json 




COPY ./config_context /opt/airflow/final_configs/config_context
COPY ./config_context /opt/airflow/sds_ei_configs_temp/config_context
RUN cp -r /opt/airflow/em_configs/api_configs/scf_mapping /opt/airflow/em_final_configs/api_configs/scf_mapping
ENTRYPOINT ["/usr/bin/dumb-init", "--", "/entrypoint"]

CMD []
