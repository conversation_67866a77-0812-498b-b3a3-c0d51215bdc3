@Library('sds-common') _
coverageLimit = 70
env.stageResult = 'PASS'
tarCmd = '/bin/tar -czvf'

def getPreviousTag(String repoName) {
    def previousTag
    withCredentials([usernamePassword(credentialsId: 'jfrog-credentials', usernameVariable: 'ARTIFACTORY_USER', passwordVariable: 'ARTIFACTORY_PASSWORD')]) {
        sh 'apt-get update && apt install curl -y '
        previousTag = (sh(returnStdout: true, script: "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -X POST -k -H '${contentTypeText}' -i ${jfrogArtifactoryURL}/${jfrogArtifactoryAQLPath} --data 'items.find({\"repo\":\"${jfrogDockerRepoName}\",\"@build.name\":{\"\$eq\":\"${repoName}\"},\"@build.number\":{\"\$match\":\"${buildversion}*\"},\"@vcs.branch\":{\"\$eq\":\"${env.GIT_BRANCH}\"}}).sort({\"\$desc\": [\"created\"]}).limit(1)' | grep name | sed -e 's/.*\"\\(.*\\)\".*/\\1/'|sed 's/\\.txt\$//' | sed 's/[^0-9]*\\([0-9].*\\)/\\1/'")).trim()
    }
    return previousTag
}

def releaseBundleCreation() {
    withCredentials([usernamePassword(credentialsId: 'jfrog-credentials', usernameVariable: 'ARTIFACTORY_USER', passwordVariable: 'ARTIFACTORY_PASSWORD')]) {
        sh 'apt-get update && apt install curl -y '
        // def solconfig = (sh(returnStdout: true, script:"curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -X POST -k -H '${contentTypeText}' -i ${jfrogArtifactoryURL}/${jfrogArtifactoryAQLPath} --data 'items.find({\"repo\":\"${jfrogGenRepoName}\", \"@build.name\":{\"\$eq\":\"${monoRepoName}-nifi-python\"}, \"@build.number\":{\"\$match\":\"${buildversion}*\"}, \"@vcs.branch\":{\"\$eq\":\"${env.GIT_BRANCH}\"}}).sort({\"\$desc\": [\"created\"]}).limit(1)' | grep name | sed -e 's/.*\"\\(.*\\)\".*/\\1/'")).trim()
        def createbundlejson = [
                            'release_bundle_name' : "${env.monoRepoName}",
                            'release_bundle_version': "${env.buildNumber}",
                            'skip_docker_manifest_resolution': false,
                            'source_type': 'aql',
                            'source': [
                                    'aql':"items.find({\"\$or\":[{\"\$and\":[{\"repo\":{\"\$match\":\"${jfrogDockerRepoName}\"}},{\"name\":{\"\$match\":\"*${env.dockerImgTag}*\"}}]},{\"\$and\":[{\"repo\":{\"\$match\":\"${jfrogHelmRepoName}\"}},{\"name\":{\"\$match\":\"*${env.buildNumber}*\"}}]}]})"
                            ]
                        ]
        writeJSON file: 'createbundle.json', json: createbundlejson
        sh "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD  -H '${acceptJsonHeader}\' -H 'Content-Type: application/json' -H '${jfrogSigningKeyHeader}' -X POST \"${jfrogArtifactoryURL}/${jfrogArtifactoryBundlePath}\" -T createbundle.json"
    }
}
// def helmBuild() {
//     def yamlFile = 'values.yaml'
//     def existingYAML = readYaml file: yamlFile
//     println("${existingYAML}")
//     existingYAML["sds-enterprise-ui"]["image"]["tag"] = "${env.dockerImgTag}"
//     writeYaml file: yamlFile, data: existingYAML , overwrite: true
//     println("${existingYAML}")
//     sh "helm dependency build ../${env.repoName}"
//     sh "helm package ../${env.repoName} --version ${env.buildNumber}"
//     uploadSpec = """{
//                                     "files":
//                                     [
//                                         {
//                                             "pattern": "${env.repoName}*.tgz",
//                                             "target": "${jfrogHelmRepoName}/${env.repoName}/",
//                                             "props": "project=${monoRepoName};repotype=${helmRepoType}"
//                                         }
//                                     ]
//                                     }"""
//     rtUpload(
//                 buildName: "${env.monoRepoName}",
//                 buildNumber: "${env.buildNumber}",
//                 serverId: "${jfrogServerId}",
//                 spec: "${uploadSpec}"
//             )

//     rtPublishBuildInfo(
//             serverId: "${jfrogServerId}",
//             buildName: "${env.monoRepoName}" ,
//             buildNumber: "${env.buildNumber}"
//             )
// }
def helmAppofappBuild() {
    def yamlFile = 'values.yaml'
    def existingYAML = readYaml file: yamlFile
    println("${existingYAML}")
    existingYAML.targetRevision = "${env.buildNumber}"
    writeYaml file: yamlFile, data: existingYAML , overwrite: true
    println("${existingYAML}")
    sh "helm package ../${monoRepoName}-deployer --version ${env.buildNumber}"
    uploadSpec = '''{
                                    "files":
                                    [
                                        {
                                            "pattern": "${monoRepoName}-deployer*.tgz",
                                            "target": "${jfrogHelmRepoName}/${monoRepoName}/",
                                            "props": "project=${monoRepoName};repotype=${helmRepoType}"
                                        }
                                    ]
                                    }'''
    rtUpload(
                buildName: "${monoRepoName}-deployer",
                buildNumber: "${env.buildNumber}",
                serverId: "${jfrogServerId}",
                spec: "${uploadSpec}"
            )

    rtPublishBuildInfo(
            serverId: "${jfrogServerId}",
            buildName: "${monoRepoName}-deployer" ,
            buildNumber: "${env.buildNumber}"
            )
}

def jobHelmBuild() {
    def yamlFile = 'values.yaml'
    def existingYAML = readYaml file: yamlFile
    println("${existingYAML}")
    def deployUtilsbundlename = imageTagUpdate('sds-platform-apps','deployUtils')
    existingYAML["jobOverrides"]["ei_config_client_deploy_job"]["mainContainer"]["image"]["tag"] = sh(script: "echo ${deployUtilsbundlename} | sed \"s/\\./-/g\"", returnStdout: true).trim()
    existingYAML["jobOverrides"]["sds_nifi_shared_copy_job"]["image"]["tag"] = sh(script: "echo ${deployUtilsbundlename} | sed \"s/\\./-/g\"", returnStdout: true).trim()
    existingYAML["jobOverrides"]["ei_config_client_deploy_job"]["initJob"]["image"]["tag"] = "${env.dockerImgTag}"
    existingYAML["jobOverrides"]["sds_client_dag_deploy_job"]["image"]["tag"] = "${env.dockerImgTag}"
    existingYAML["jobOverrides"]["em_api_config_deploy_job"]["image"]["tag"] = "${env.dockerImgTag}"
    def eiConfigVersion = imageTagUpdate('sds-solution-ei','configs')
    existingYAML["jobOverrides"]["ei_config_client_deploy_job"]["mainContainer"]["ei_version"] = sh(script: "echo ${eiConfigVersion} | sed \"s/\\./-/g\"", returnStdout: true).trim()
    def emConfigVersion = imageTagUpdate('sds-product-em','configs')
    existingYAML["jobOverrides"]["ei_config_client_deploy_job"]["mainContainer"]["em_version"] = sh(script: "echo ${emConfigVersion} | sed \"s/\\./-/g\"", returnStdout: true).trim()
    existingYAML.jobOverrides.image.tag = "${env.dockerImgTag}"
    existingYAML.bundleVersion.client = "${env.buildNumber}"
    writeYaml file: yamlFile, data: existingYAML , overwrite: true
    println("${existingYAML}")
    sh "helm package ../${monoRepoName}-jobs --version ${env.buildNumber}"
    uploadSpec = """{
                                    "files":
                                    [
                                        {
                                            "pattern": "${monoRepoName}-jobs*.tgz",
                                            "target": "${jfrogHelmRepoName}/${monoRepoName}/",
                                            "props": "project=${monoRepoName};repotype=${helmRepoType}"
                                        }
                                    ]
                                    }"""
    rtUpload(
                buildName: "${monoRepoName}-jobs",
                buildNumber: "${env.buildNumber}",
                serverId: "${jfrogServerId}",
                spec: "${uploadSpec}"
            )

    rtPublishBuildInfo(
            serverId: "${jfrogServerId}",
            buildName: "${monoRepoName}-jobs",
            buildNumber: "${env.buildNumber}"
            )
}

def dockerfileUpdate(command,tagimage,argument){
    if(!tagimage.isEmpty()){
    def bundlename = sh(script: "echo ${tagimage} | sed \"s/\\./-/g\"", returnStdout: true).trim()
    command= "${command}" + " --build-arg " + "${argument}" + "=" + "${bundlename}"
    println("${command}")
    return command
    }
}
def imageTagUpdate(rootfolder,component){
    def yamlFile = '../../../dependencies.yaml'
    chartYaml = readYaml file: yamlFile
    if(chartYaml["${rootfolder}"]!=null && chartYaml["${rootfolder}"]['component-override']!=null && chartYaml["${rootfolder}"]['component-override']["${component}"]!=null && !chartYaml["${rootfolder}"]['component-override']["${component}"].isEmpty())
     {return chartYaml["${rootfolder}"]['component-override']["${component}"]
     }
     else if(chartYaml["${rootfolder}"]!=null && chartYaml["${rootfolder}"]['bundle-version']!=null && !chartYaml["${rootfolder}"]['bundle-version'].isEmpty())
     {return chartYaml["${rootfolder}"]['bundle-version']}
     else{
       println("The ${component} is empty to deploy eiconfigdeployjob ")
       sh "exit 1" 
     }   
}

def kanikoBuildAndPushToDockerHubMono(Map config = [:]) {
    def defaultDockerfilePath = '/Dockerfile'
    def dockerfile = config.containsKey('dockerfilePath') ? config.dockerfilePath : defaultDockerfilePath
    sh "export IFS=''"
    withCredentials([usernamePassword(credentialsId: 'jfrog-credentials', usernameVariable: 'ARTIFACTORY_USER', passwordVariable: 'ARTIFACTORY_PASSWORD')]) {
        env.PIP_EXTRA_INDEX_URL = "https://$ARTIFACTORY_USER:$ARTIFACTORY_PASSWORD@${jfrogDomain}/artifactory/api/pypi/${jfrogPypiRepoName}/simple"
        sh "/kaniko/executor --build-arg build_version=${config.buildNumber} --build-arg ARTIFACTORY_USER=$ARTIFACTORY_USER --build-arg ARTIFACTORY_PASSWORD=$ARTIFACTORY_PASSWORD --dockerfile=`pwd`${dockerfile} -c `pwd` --insecure --skip-tls-verify --cache=false --label \"${config.repoName}.Branch=${env.GIT_BRANCH}\" --label \"${config.repoName}.BuildVersion=${config.buildNumber}\" --build-arg BASE_IMAGE=${config.baseimage} ${config.command} --destination=${config.imageName}"
    }
}

def artifactQueryMain(bundleversion, buildname, bundlename) {
    withCredentials([usernamePassword(credentialsId: 'jfrog-credentials', usernameVariable: 'ARTIFACTORY_USER', passwordVariable: 'ARTIFACTORY_PASSWORD')]) {
        if (bundleversion.isEmpty()) {
            println('The bundle is not valid')
            sh 'exit 1'
        }
        else {
            def tarfile = (sh(returnStdout: true, script:"curl  -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -H '${acceptJsonHeader}' -H '${contentTypeJsonHeader}'  -X GET \"${jfrogArtifactoryURL}/${jfrogArtifactoryBundlePath}/records/${bundlename}/${bundleversion}\"| grep path | grep ${buildname} | sed 's/.*\"\\(.*\\)\".*/\\1/'")).trim()
            if (tarfile.isEmpty()) {
                sh 'exit 1 '}
           else {
                return tarfile}
        }
    }
}
def artifactDownloadMain(tarfile) {
    env.tarf = "${tarfile}"
    rtDownload(
                    serverId: "${jfrogServerId}",
                    spec: '''{
                        "files": [
                            {
                            "pattern": "${jfrogGenRepoName}/${tarf}",
                            "target": "custom-files/"

                            }
                        ]
                        }''',

                )
    sh ''' cd custom-files/
            ls
            tar xzvf ${tarf}
            rm -rf ${tarf}
            rm -rf config README.md
            ls
        '''
}
def artifactDownload(tarfile){
    env.tarf = "${tarfile}"
    rtDownload (
                    serverId: 'pe-jfrog',
                    spec: '''{
                        "files": [
                            {
                            "pattern": "pai-general-local-dev/${tarf}",
                            "target": "custom-files/"

                            }
                        ]
                        }''',

                )
        sh ''' cd custom-files/
            ls
            tar xzvf ${tarf}
            rm -rf ${tarf}
            rm -rf config README.md
            ls
        '''
}
def dependencyfileupdate(component)
{
   def yamlFile= "../dependencies.yaml"
   existingYAML = readYaml file: yamlFile 
   def command=''
   println ("${existingYAML}")
   if(existingYAML.isEmpty())
   {println("The dependency file is empty")
    sh "exit 1"}
   else{
   existingYAML.each{sdsName, sdsData ->
   def componentName = sdsName.toUpperCase().replace("-", "_")
   if(("${componentName}" == 'SDS_SOLUTION_CAM') && ("${component}" == 'orchestration'))
    {println("Skipping since its cam orchestration is not present")}
   else  
    {
   if(sdsData['component-override']!=null && sdsData['component-override']["${component}"]!=null && !sdsData['component-override']["${component}"].isEmpty())
   {command = dockerfileUpdate(command,sdsData['component-override']["${component}"],"${componentName}")
   }
   else if(sdsData['bundle-version']!=null && !sdsData['bundle-version'].isEmpty())
   {command = dockerfileUpdate(command,sdsData['bundle-version'],"${componentName}")}
   else{
    println("The bundle version and component override is empty")
    sh "exit 1"
   }
   }}}
   println("${command}")
   return command
}
def artifactDependency(component)
{   def tarfiles = [:]
    def yamlFile= "../dependencies.yaml"
    existingYAML = readYaml file: yamlFile
    if(existingYAML.isEmpty())
   {println("The dependency file is empty")
    sh "exit 1"}
    else{
    existingYAML.each{sdsName, sdsData ->
    def componentName = "${sdsName}-${component}"
    if(sdsName != 'sds-platform-apps' && sdsName != 'sds-product-em')
    { if(sdsData['component-override']!=null && sdsData['component-override']["${component}"]!=null && !sdsData['component-override']["${component}"].isEmpty())
     {tarfiles.put("${sdsName}",artifactQueryMain(sdsData['component-override']["${component}"],componentName,sdsName))
     }
     else if(sdsData['bundle-version']!=null && !sdsData['bundle-version'].isEmpty())
     {tarfiles.put("${sdsName}",artifactQueryMain(sdsData['bundle-version'],componentName,sdsName))}
     else{
       println("The bundle version and component override is empty ")
       sh "exit 1" 
     }
     }
    }}
    return tarfiles

}
pipeline{
    options {
        disableConcurrentBuilds()
    }
    agent {
        kubernetes {
            label "debian"
            defaultContainer "debian"
        }
    }

    environment {
        sharedimagetag = null
        monoRepoName = "sds-client-ibx"

        //euiCompName = 'eui'
        orchestrationCompName = 'orchestration'
        configsCompName = 'configs'
        
        jenkinsFileName = 'k8sJenkinsfile'
        dependenciesFileName = 'dependencies.yaml'

        jfrogServerId = 'pe-jfrog'
        jfrogGenRepoName = 'pai-general-local-dev'
        jfrogHelmRepoName = 'pai-helm-local-dev'
        jfrogDockerRepoName = 'docker-generic-local'
        jfrogPypiRepoName = 'pai-pypi-local-dev'
        helmRepoType = 'helm'

        jfrogDomain = 'prevalentai.jfrog.io'
        jfrogArtifactoryURL = 'https://prevalentai.jfrog.io'
        jfrogArtifactoryAQLPath = 'artifactory/api/search/aql'
        jfrogArtifactoryBundlePath = 'lifecycle/api/v2/release_bundle'

        contentTypeText = 'Content-Type:text/plain'
        contentTypeJsonHeader = 'Content-Type: application/json'
        acceptJsonHeader = 'Accept: application/json'
        jfrogSigningKeyHeader = 'X-JFrog-Signing-Key-Name: Monorepo'

        COSIGN_PASSWORD = credentials('cosign-password')

        dockerBuildAgentLabel = 'dockerBuild'
        kanikoDefaultContainer = 'kaniko'
        cosignDefaultContiner = 'cosign'
        debianAgentLabel = 'debian'
        debianAgentDefaultContainer = 'debian'
        helmAgentLabel = 'helm'
        helmAgentDefaultContainer = 'helm3-11-7'
        clientCode = 'sol'
    }

    
    
    stages {
        stage('full build-check') {
            steps {
                script {
                    sharedimagetag = [:]
                    def tagData = getTagDetails.gitTagRelease("IBX")
                    env.buildNumber = tagData[0]
                    env.tagmsg = tagData[1]
                    env.buildversion = tagData[2]
                    env.chkfullbuild = checkFullbuild()
                    println(" ${env.tagInfo} ")
                    println(" ${env.chkfullbuild} ")
                    env.dockerImgTag = sh(script: "echo ${env.buildNumber} | sed \"s/+/-/g\" | sed \"s/\\./-/g\"", returnStdout: true).trim()                    
                    println("${env.dockerImgTag}")
                }
            }
        }
        stage('parallel pipelines') {
            parallel {
// ########### configs component build stages ###################                
                stage('configs') {
                    agent any
                    when {
                        anyOf {
                            expression {                                        
                                return showChangeLogs("${configsCompName}/,${jenkinsFileName},${dependenciesFileName}")
                            }                             
                            expression {
                                env.chkfullbuild == 'true'
                            }
                        }
                    }
                    environment {
                        repoName = "${monoRepoName}-${configsCompName}"
                    }
                        stages {
                            // stage('configs zip-nifi-dependencies') {
                            //         steps {
                            //             script { dir("${configsCompName }/") {
                            //             sh "cd ./scripts/nifi/ && ${tarCmd} ../../${monoRepoName}-nifi-python-files-${buildNumber}.tar.gz ."
                            //             }}
                            //         }
                            // }

                            // stage('configs nifi-dependencies-upload-to-artifactory') {
                            //     steps {
                            //         script { dir("${configsCompName }/") {
                            //             def uploadSpec = """{
                            //             "files":
                            //             [
                            //             {
                            //                 "pattern": "${monoRepoName}-nifi-python-files-${buildNumber}.tar.gz",
                            //                 "target": "${jfrogGenRepoName}",
                            //                 "props": "project=${monorepoName}"
                            //             }
                            //             ]
                            //         }"""

                            //             rtUpload(
                            //             buildName: "${monoRepoName}-nifi-python",
                            //             buildNumber: "${buildNumber}",
                            //             serverId: "${jfrogServerId}",
                            //             spec: "${uploadSpec}"
                            //         )
                            //         }}
                            //     }
                            // }
                            // stage('configs publish-build-info') {
                            //     steps {
                            //         echo env.buildNumber
                            //         rtPublishBuildInfo(
                            //             serverId: "${jfrogServerId}",
                            //             buildName: "${monoRepoName}-nifi-python" ,
                            //             buildNumber: "${buildNumber}"

                            //         )
                            //     }
                            // }
                            stage('configs build') {
                            agent {
                                    kubernetes {
                                    label "${dockerBuildAgentLabel}"
                                    defaultContainer "${cosignDefaultContiner}"
                                    }
                            }
                            environment {
                        repoName = "${monoRepoName}-${configsCompName}"
                    }
                            steps {
                                script {
                                    def yamlFile = 'dependencies.yaml'
                                    existingYAML = readYaml file: yamlFile
                                    dir("${configsCompName}/") {
                                        def imageName = "${docker_registery}/${repoName}:${env.dockerImgTag}"
                                        def command = dependencyfileupdate(configsCompName)
                                        kanikoBuildAndPushToDockerHubMono(imageName: "${imageName}", buildNumber:"${buildNumber}", repoName:"${repoName}", command:"${command}")
                                        dockerRetagBuild("${repoName}-${env.dockerImgTag}")
                
                                        cosign.imageSigning(imageName: "${imageName}")
                                        cosign.imageVerification(imageName: "${imageName}")
                                }}
                                }
                            }
                            }
                        }

                stage('configs build-incremental') {
                    when {
                        allOf {
                            allOf {
                                not {
                                    anyOf {
                                        expression {                                        
                                            return showChangeLogs("${configsCompName}/,${jenkinsFileName},${dependenciesFileName}")
                                        }                                         
                                    }
                                }
                                expression {
                                    env.chkfullbuild != 'true'
                                }
                            }
                            expression {                                        
                                return showChangeLogs("*/**")
                            } 
                        }
                    }
                    environment {
                        repoName = "${monoRepoName}-${configsCompName}"

                        incremnttag = ''
                    }
                    stages {
                        stage('configs build-incremental retrieve-image') {
                            agent {
                                kubernetes {
                                    label "${helmAgentLabel}"
                                    defaultContainer "${helmAgentDefaultContainer}"
                                }
                            }
                            steps { script {
                                    configPreviousTag = getPreviousTag("${repoName}")
                            }
                        }
                    }
                        stage('configs build-incremental retag-image') {
                                agent {
                                kubernetes {
                                    label "${dockerBuildAgentLabel}"
                                    defaultContainer "${cosignDefaultContiner}"
                                }
                                }
                            steps {
                                script {
                                    dir("${configsCompName}/") {
                                        def imageName = "${docker_registery}/${repoName}:${env.dockerImgTag}"
                                        def imgTag = "${repoName}-" + "${env.dockerImgTag}"
                                        sh "echo FROM ${docker_registery}/${repoName}:${configPreviousTag} | \\/kaniko/executor --dockerfile /dev/stdin --destination ${imageName}"
                                        dockerRetagBuild(imgTag)
                                        cosign.imageSigning(imageName: "${imageName}")
                                        cosign.imageVerification(imageName: "${imageName}")
                                    }
                                }
                            }
                        }
                }
                }
// ########### orchestration component build stages ###################
                stage('orchestration') {
                    when {
                            anyOf {
                                expression {
                                    return showChangeLogs("${orchestrationCompName}/,${jenkinsFileName},${dependenciesFileName}")
                                }
                                expression {
                                    env.chkfullbuild == 'true'
                                }
                            }
                    }
                    agent {
                        kubernetes {
                            label "${debianAgentLabel}"
                            defaultContainer "${debianAgentDefaultContainer}"
                        }
                    }
                    environment {
                        repoName = "${monoRepoName}-${orchestrationCompName}"

                    }
                    stages {
                            stage('orchestrtion build') {
                            agent {
                                kubernetes {
                                    label "${dockerBuildAgentLabel}"
                                    defaultContainer "${cosignDefaultContiner}"
                                }
                            }
                            steps {
                                script {
                                    dir("${orchestrationCompName}/") {
                                        imageName = "${docker_registery}/${repoName}:${env.dockerImgTag}"
                                        imgTag = "${repoName}-" + "${env.dockerImgTag}"
                                        dockerBuild.kanikoBuildAndPushToDockerHubClient(imageName: "${imageName}", buildNumber:"${buildNumber}", repoName:"${repoName}", clientCode:"${clientCode}")
                                        dockerRetagBuild(imgTag)
                                        cosign.imageSigning(imageName: "${imageName}")
                                        cosign.imageVerification(imageName: "${imageName}")
                                }}
                                }
                            }

                            }
                    }

                stage('orchestration build-incremental') {
                    when {
                        allOf {
                            allOf {
                                not {
                                    expression {
                                        return showChangeLogs("${orchestrationCompName}/")
                                    }
                                }
                                not {
                                    anyOf {
                                        expression {
                                            return showChangeLogs("${jenkinsFileName},${dependenciesFileName}")
                                        }
                                    }
                                }
                                expression {
                                    env.chkfullbuild != 'true'
                                }
                            }
                            expression {
                                return showChangeLogs("*/**")
                            }
                        }
                    }
                    agent {
                        kubernetes {
                            label "${debianAgentLabel}"
                            defaultContainer "${debianAgentDefaultContainer}"
                        }
                    }
                    environment {
                        repoName = "${monoRepoName}-${orchestrationCompName}"
                    }
                    stages {
                        stage('orchestrtion build-incremental retrieve-image') {
                            agent {
                                kubernetes {
                                    label "${helmAgentLabel}"
                                    defaultContainer "${helmAgentDefaultContainer}"
                                }
                            }
                            steps { script {
                                    orchestrationPreviousImage = getPreviousTag("${repoName}")
                            }
                    }}
                        stage('orchestrtion build-incremental retag-image') {
                            agent {
                                kubernetes {
                                    label "${dockerBuildAgentLabel}"
                                    defaultContainer "${cosignDefaultContiner}"
                                }
                            }
                            steps {
                                script { dir("${orchestrationCompName}/") {
                                        imageName = "${docker_registery}/${repoName}:${env.dockerImgTag}"
                                        imgTag = "${repoName}-" + "${env.dockerImgTag}"
                                        sh "echo FROM ${docker_registery}/${repoName}:${orchestrationPreviousImage} | \\/kaniko/executor --dockerfile /dev/stdin --destination ${imageName}"
                                        dockerRetagBuild(imgTag)
                                        println('Signing orchestration image')
                                        cosign.imageSigning(imageName: "${imageName}")
                                        cosign.imageVerification(imageName: "${imageName}")
                                }
                            }
                    }}
                    }
                }
                // Parallel pipelines ends here
                }

                }
            stage('helm build') {
                agent {
                        kubernetes {
                            label "${debianAgentLabel}"
                            defaultContainer "${debianAgentDefaultContainer}"
                        }
                }
                when {
                        anyOf {
                            expression {
                                return showChangeLogs("*/**")
                            }
                            expression { env.chkfullbuild == 'true' }
                        }
                }
                environment {
                    repository = 'test-monorepo'
                    registryCredential = 'dockerhub'
                    repoName = "${monoRepoName}"
                    COSIGN_PASSWORD = credentials('cosign-password')
                    //nonJobHelmPath = 'deployments/helm/sds-client-ite/'
                    jobHelmPath = "deployments/helm/${monoRepoName}-jobs/"
                    deployerHelmPath = "deployments/helm/${monoRepoName}-deployer/"
                }
                stages {
                    stage('values-file-update') {
                        agent {
                            kubernetes {
                                label "${helmAgentLabel}"
                                defaultContainer "${helmAgentDefaultContainer}"
                            }
                        }
                        steps {
                            script {
                            // dir("${nonJobHelmPath}") {
                            //     helmBuild()
                            // }

                            dir("${jobHelmPath}") {
                                jobHelmBuild()
                            }
                            dir("${deployerHelmPath}") {
                                helmAppofappBuild()
                            }

                            }}
                        }
                        stage('annotate tag') {
                        agent {
                                kubernetes {
                                    label "${debianAgentLabel}"
                                    defaultContainer "${debianAgentDefaultContainer}"
                                }
                        }
                            steps {
                                script {
                                    annotateTag()
                                }}
                            }
                        }
                    }
            stage('release-bundle creation') {
                when {
                        anyOf {
                            expression {
                                return showChangeLogs("*/**")
                            }
                            expression {
                                env.chkfullbuild == 'true'
                            }
                        }
                }
                agent {
                kubernetes {
                    label "${helmAgentLabel}"
                    defaultContainer "${helmAgentDefaultContainer}"
                }
                }
                steps {
                    script {
                    releaseBundleCreation()
                    deploy.notifyOnBuild(buildVersion:"${env.buildNumber}")
                    currentBuild.description = "${env.buildNumber}"
                    }
                }
            }
            stage('release-bundle notification') {
                when {
                        allOf {
                        not {
                            expression {
                                return showChangeLogs("*/**")
                            }
                        }
                        expression {
                                env.chkfullbuild != 'true'
                        }
                    } }
                agent {
                kubernetes {
                    label "${debianAgentLabel}"
                    defaultContainer "${debianAgentDefaultContainer}"
                }
                }
                steps {
                    script {
                    gittag = getTagDetails.getLatestReleaseBundle()
                    deploy.notifyOnBuild(buildVersion:"${gittag}")
                    currentBuild.description = "${gittag}"
                    }
                }
                }
            }
                }
