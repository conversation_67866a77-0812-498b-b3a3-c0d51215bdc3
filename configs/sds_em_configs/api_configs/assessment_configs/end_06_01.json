{"id": "END-06-01", "title": "Devices with EDR agents have file integrity monitoring enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment verifies whether File Integrity Monitoring (FIM) is enabled on devices that have been onboarded to an EDR agent. FIM ensures changes to critical files and configurations are detected, helping protect the integrity of the system.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'Crowdstrike FIM Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_fim_policy_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_fim_policy_status=true", "finding_primary_key": "p_id", "finding_title": "FIM not enabled", "finding_config": [{"title": "Crowdstrike FIM Status", "expression": "crowdstrike_fim_policy_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-06"]}}