df_studio_feature_test_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: Athira_Damodaran
    retries: 1
    start_date: 2024-01-01
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: prevalentai/sds-solution-studio-qa-automation:<%STUDIO_QA_IMAGE_VERSION%>
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      run_prerequisites: false
      generate_report: true
      report_base_path: "<%QA_REPORT_URI%>/behave/"
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true      
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        TEST_ENV: "<%KUBE_NAMESPACE%>"
        DEBUG_MODE: "true"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        DOMAIN_URL: "{{macros.custom_macros.get_env('DOMAIN_URL')}}"
        SELENIUM_SDS3_URL: "{{macros.custom_macros.get_env('SELENIUM_SDS3_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        AIRFLOW_CONN_ADMIN_API_BASE_URL: "{{macros.custom_macros.get_env('AIRFLOW_CONN_ADMIN_API_BASE_URL')}}"
        AIRFLOW_CONN_RCM_ADMIN_API: "{{macros.custom_macros.get_env('AIRFLOW_CONN_RCM_ADMIN_API')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        MANAGEMENT_API_UI_MAPPING_ENDPOINT: "{{macros.custom_macros.get_env('MANAGEMENT_API_UI_MAPPING_ENDPOINT')}}"
        DATALAKE_URI: "s3a://{{macros.custom_macros.get_env('S3_DATALAKE_BUCKET_NAME')}}"
        PYICEBERG_CATALOG__DEFAULT__URI: "{{macros.custom_macros.get_env('PYICEBERG_CATALOG__DEFAULT__URI')}}"
        KEYCLOAK_SSL_VERIFY: "{{macros.custom_macros.get_env('KEYCLOAK_SSL_VERIFY', True)}}"
        DATALAKE_LOG_URI: "<%LOG_URI%>"
        AIRFLOW_API_ENDPOINT: http://airflow-webserver.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/orchestration
        CONFIG_MANAGER_ENDPOINT: <%CONFIG_ARTIFACTORY_URI%>/<%AUT_CONFIG_MANAGER_ENDPOINT%>
        KG_QA_IMAGE_VERSION: <%KG_QA_IMAGE_VERSION%>
        CLIENT_REPO_NAME: <%CLIENT_REPO_NAME%>
        PLATFORM_QA_IMAGE_VERSION: <%PLATFORM_QA_IMAGE_VERSION%>
        GITHUB_URL: <%GITHUB_URL%>
        EM_QA_IMAGE_VERSION : <%EM_QA_IMAGE_VERSION%>
        INGESTION_ENDPOINT: <%CONFIG_ARTIFACTORY_URI%>/<%INGESTION_ENDPOINT%>
        SCHEMA_ENDPOINT: <%CONFIG_ARTIFACTORY_URI%>/<%SCHEMA_ENDPOINT%>
        ENTITY_ENDPOINT: <%CONFIG_ARTIFACTORY_URI%>/<%ENTITY_ENDPOINT%>
      secrets:
      - deploy_type: env
        deploy_target: SELENIUM_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumUsername
      - deploy_type: env
        deploy_target: SELENIUM_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumPassword
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: GIT_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: gitPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
  task_groups:
    studio_tests:
      tooltip: "Grouped Studio feature tests"
  tasks:
    start_task:
      operator: airflow.operators.empty.EmptyOperator
    workspace_test:
      dependencies: 
      - start_task
      feature_files:
      - src/studio_ui/features/workspace.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - workspace
      task_group_name: studio_tests
    config_listing_test:
      dependencies: 
      - start_task
      feature_files:
      - src/studio_ui/features/config_listing_page.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - config-listing
      task_group_name: studio_tests
    download_config_test:
      dependencies: 
      - start_task
      feature_files:
      - src/studio_ui/features/config_download_functionality.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - download-config
      task_group_name: studio_tests
    entity_management_test:
      dependencies: 
      - start_task
      feature_files:
      - src/studio_ui/features/entity_management.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - feature-test
      task_group_name: studio_tests
    pipeline_flow_test:
      dependencies: 
      - start_task
      feature_files:
      - src/studio_api/features/studio_config_pipeline.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - config-pipeline
      task_group_name: studio_tests
    end_task:
      dependencies:
      - workspace_test
      - config_listing_test
      - pipeline_flow_test
      - download_config_test
      - entity_management_test
      operator: airflow.operators.empty.EmptyOperator
      trigger_rule: "all_done"
  
