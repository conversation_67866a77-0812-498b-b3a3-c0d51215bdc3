{"primaryKey": "_ref", "origin": "'Infoblox DDI Enrichment'", "temporaryProperties": [{"colName": "temp_dns_name", "colExpr": "CASE WHEN regexp_like( SUBSTRING_INDEX(SUBSTRING_INDEX(_ref, ':', -1), '/', 1), '^(?:[^.]*\\\\.){2}[^.]*$' ) THEN SUBSTRING_INDEX(SUBSTRING_INDEX(_ref, ':', -1), '/', 1) ELSE NULL END"}, {"colName": "temp_host_name", "colExpr": "UPPER(SUBSTRING_INDEX ( SUBSTRING_INDEX (_ref, ':', -1), CASE WHEN LOCATE('.', SUBSTRING_INDEX (_ref, ':', -1)) > 0 THEN '.' ELSE '/' END , 1))"}], "commonProperties": [{"colName": "description", "colExpr": "comment"}, {"colName": "business_unit", "colExpr": "CASE WHEN lower(comment) LIKE '%bu%' THEN REGEXP_EXTRACT(comment, 'BU\\\\s*=\\\\s*(.+)', 1) ELSE NULL END"}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(CASE WHEN lower(temp_host_name)='npi84ab8a' then 'npi84ab8a-(hp-laserjet-mfp-m436)'  else temp_host_name end)"}, {"colName": "dns_name", "colExpr": "CASE WHEN lower(temp_dns_name)='npi84ab8a.prevalent.com' then 'NPI84AB8A-(HP-LaserJet-MFP-M436)'  else temp_dns_name end"}, {"colName": "vlan", "colExpr": "lower(CASE WHEN lower(comment) LIKE '%vlan%' THEN regexp_extract(comment, 'VLAN\\\\s*=\\\\s*([^;]+)', 1) ELSE NULL END)"}, {"colName": "role", "colExpr": "CASE \nWHEN lower(description) LIKE '%role%' and \nlower(description) LIKE '%test server%' THEN 'Test Server' \nWHEN lower(description) LIKE '%role%' and \nlower(description) LIKE '%test machine%' THEN 'Test Machine'\nWHEN lower(description) LIKE '%role%' and lower(description) LIKE '%web server%' THEN 'Web Server' \nWHEN lower(description) LIKE '%role%' and lower(description) LIKE '%elk slave%' THEN 'ELK Slave'\nWHEN lower(description) LIKE '%role%' and lower(description) LIKE '%elk master%' THEN 'ELK Master' \nWHEN lower(description) LIKE '%elk master%' THEN 'ELK Master' \nWHEN lower(description) LIKE '%elk slave%' THEN 'ELK Slave'\nWHEN lower(description) LIKE '%role%' THEN REGEXP_EXTRACT(description, 'Role\\\\s*=\\\\s*([^;]+)', 1) \nWHEN lower(description) LIKE '%biometric%' THEN 'Biometric Device' \nELSE type END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',description,infoblox_ref,dns_name)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "infoblox_ref", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.country_name = e.region", "sourcePreTransform": [{"colName": "country_name", "colExpr": "CASE WHEN comment LIKE '%Location%' THEN REGEXP_EXTRACT(comment, 'Location\\\\s*=\\\\s*([^;]+)', 1)ELSE NULL END"}]}], "dataSource": {"name": "Infoblox Onprem DDI", "feedName": "Host Enrichment", "srdm": "<%SRDM_SCHEMA_NAME%>.infoblox_onprem_ddi__host_enrichment"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__infoblox_ddi_enrichment__ref"}