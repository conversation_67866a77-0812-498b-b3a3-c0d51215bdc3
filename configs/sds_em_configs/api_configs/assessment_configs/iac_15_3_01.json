{"id": "IAC-15.3-01", "title": "Devices with active Azure accounts have no successful login activity within the last 180 days", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment ensures that devices with active Azure AD accounts are identified if they have not recorded a successful login in the last 180 days. This may indicate inactive or orphaned accounts that require review or deactivation.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active devices from Host where Origin contains MS Azure AD and AAD Operational Status equals Active", "Validate that Last Login is within the last 180 days"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,aad_operational_status,origin,origin as affected_asset_origin,datediff(from_unixtime(updated_at/1000,'yyyy-MM-dd'),from_unixtime(login_last_date/1000,'yyyy-MM-dd')) AS elapsed_days from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where  activity_status='Active' and ARRAY_CONTAINS(origin,'MS Azure AD') and aad_operational_status='Active'", "success_condition": "elapsed_days<=180", "finding_primary_key": "p_id", "finding_title": "No Login in 180 Days", "finding_config": [{"title": "Days Since Last Login", "expression": "elapsed_days", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["IAC-15.3"]}}