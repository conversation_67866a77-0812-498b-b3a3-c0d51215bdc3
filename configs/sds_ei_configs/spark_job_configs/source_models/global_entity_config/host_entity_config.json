{"entityClass": "Host", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(instance_name,fqdn,dns_name,host_name,aad_device_id,hardware_serial_number,ip,mac_address,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,azure_vm_os_name,azure_vm_os_version,qualys_tags,crowdstrike_tags,ad_distinguished_name,hardware_chassis_type,hardware_manufacturer,native_type,dns_name,crowdstrike_product_type_desc,itop_class,aad_device_category,aad_profile_type,cast(aad_system_label as string),tenable_io_system_type,tenablesc_asset_groups)", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}], "entitySpecificProperties": [{"colName": "role", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vlan", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "configure_for_dhcp", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vlan_updated", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "asset_role", "colExpr": "CASE WHEN internal_contributor IS NULL AND host_name IS NULL THEN NULL WHEN LOWER(internal_contributor) RLIKE '.*database.*|.*db server.*|.*database server.*' OR (LOWER(host_name) RLIKE '.*dbs.*' AND <PERSON>OWER(internal_contributor) RLIKE '.*server.*') THEN 'Database' WHEN LOWER(host_name) RLIKE '.*prd.*|.*prod.*' AND <PERSON>OWER(internal_contributor) RLIKE '.*server.*' THEN 'Production Server' WHEN LOWER(internal_contributor) RLIKE '.*domain controller.*|.*domain_controller.*|.*domaincontroller.*|.*active directory.*|.*ad server.*|.*directory server.*' THEN 'Domain Controller' WHEN LOWER(internal_contributor) RLIKE '.*proxy.*|.*proxies.*' THEN 'Proxy' WHEN LOWER(internal_contributor) RLIKE '.*app.*server.*|.*application.*server.*|.*web application.*' THEN 'Application Server' WHEN LOWER(internal_contributor) RLIKE '.*load balancer.*|.*load.*balancer.*|.*big.*ip.*|.*netscaler.*' THEN 'Load Balancer' WHEN LOWER(internal_contributor) RLIKE '.*web.*server.*|.*webserver.*|.*web_server.*' THEN 'Web Server' WHEN LOWER(internal_contributor) RLIKE '.*archive server.*' THEN 'Archive Server' WHEN LOWER(internal_contributor) RLIKE '.*citrix workspace.*' THEN 'Citrix Workspace' WHEN LOWER(internal_contributor) RLIKE '.*remote console manager.*|.*idrac.*' THEN 'Remote Console Manager' WHEN LOWER(internal_contributor) RLIKE '.*esxi.*' THEN 'ESXi' WHEN LOWER(internal_contributor) RLIKE '.*xen server.*' THEN 'Xen Server' WHEN LOWER(internal_contributor) RLIKE '.*aix.*server.*' THEN 'AIX Server' WHEN LOWER(internal_contributor) RLIKE '.*nicesystems.*' THEN 'Nice Systems' WHEN LOWER(internal_contributor) RLIKE '.*ivanti.*' THEN 'Ivanti' WHEN LOWER(internal_contributor) RLIKE '.*web portal.*' THEN 'Web Portal' WHEN LOWER(internal_contributor) RLIKE '.*logging system.*|.*centralize log.*|.*centralized log.*' THEN 'Logging System' WHEN LOWER(host_name) RLIKE '.*router.*' OR (LOWER(internal_contributor) RLIKE '.*router.*|.*junos.*' AND LOWER(internal_contributor) NOT RLIKE '.*windows.*') THEN 'Router' WHEN LOWER(internal_contributor) RLIKE '.*security gateway.*' THEN 'Security Gateway' WHEN LOWER(internal_contributor) RLIKE '.*api gateway.*' THEN 'API Gateway' WHEN LOWER(internal_contributor) RLIKE '.*hypervisor.*|.*vmware.*|.*esx.*' THEN 'Hypervisor' WHEN LOWER(internal_contributor) RLIKE '.*jump host.*|.*jumphost.*' THEN 'Jump Host' WHEN LOWER(internal_contributor) RLIKE '.*firewall.*|.*firewall vpn.*|.*fortigate.*|.*cisco adaptive security appliance.*' THEN 'Firewall' WHEN LOWER(internal_contributor) RLIKE '.*wap.*' THEN 'Wireless Access Point' WHEN LOWER(internal_contributor) RLIKE '.*network switch.*|.*nexus.*|.*switch.*|.*cisco nx-os.*' THEN 'Network Switch' WHEN LOWER(internal_contributor) RLIKE '.*enterprise storage.*' THEN 'Enterprise Storage' WHEN LOWER(internal_contributor) RLIKE '.*analytics.*|.*analytical.*' THEN 'Analytical System' WHEN LOWER(internal_contributor) RLIKE '.*backup.*|.*back up.*|.*veeam.*' THEN 'Backup' WHEN LOWER(internal_contributor) RLIKE '.*data platform.*' THEN 'Data Platform' WHEN LOWER(internal_contributor) RLIKE '.*it management system.*' THEN 'IT Management System' WHEN LOWER(internal_contributor) RLIKE '.*rnd software.*' THEN 'R&D Software' WHEN LOWER(internal_contributor) RLIKE '.*monitoring.*' THEN 'Monitoring' WHEN LOWER(internal_contributor) RLIKE '.*network traffic performance.*' THEN 'Network Performance' WHEN LOWER(internal_contributor) RLIKE '.*power management.*' THEN 'Power Management System' WHEN LOWER(internal_contributor) RLIKE '.*mail.*server.*|.*smtp.*' THEN 'Mail Server' WHEN LOWER(internal_contributor) RLIKE '.*dns.*server.*|.*dns.*' THEN 'DNS Server' WHEN LOWER(internal_contributor) RLIKE '.*file.*server.*|.*fileserver.*|.*ftp.*' THEN 'File Server' WHEN LOWER(internal_contributor) RLIKE '.*printer.*|.*print server.*|.*xerox.*|.*canon.*|.*hp.*laser.*|.*hp.*jetdirect.*|.*samsung x4220r.*|.*varioprint.*|.*sato network printing version.*|.*lexmark.*|.*lantronix.*|.*kyocera.*|.*hp ethernet.*' THEN 'Printer' WHEN LOWER(internal_contributor) RLIKE '.*telephony.*' THEN 'Telecom System' WHEN LOWER(internal_contributor) RLIKE '.*general.*server.*|.*pci.*server.*|.*iso.*server.*|.*regular.*server.*' THEN 'General Server' WHEN LOWER(internal_contributor) RLIKE '.*vpn.*' THEN 'VPN' WHEN LOWER(internal_contributor) RLIKE '.*scanner.*|.*nessus.*' THEN 'Scanner' WHEN LOWER(internal_contributor) RLIKE '.*siem.*' THEN 'SIEM' WHEN LOWER(internal_contributor) RLIKE '.*dlp.*' THEN 'Data Loss Prevention' WHEN LOWER(internal_contributor) RLIKE '.*webmail.*' THEN 'Web Mail' WHEN LOWER(internal_contributor) RLIKE '.*mdm.*|.*ios .*|.*android.*' AND LOWER(internal_contributor) NOT RLIKE '.*windows.*|.*cisco ios.*' THEN 'Mobile Device Management' WHEN LOWER(internal_contributor) RLIKE '.*intranet.*' THEN 'Intranet' WHEN LOWER(internal_contributor) RLIKE '.*persistentvdi.*|.*vdi.*' OR LOWER(host_name) RLIKE '.*wvd.*' THEN 'Virtual Desktop' WHEN LOWER(internal_contributor) RLIKE '.*general.*purpose.*|.*desktop.*|.*laptop.*|.*tablet.*' AND LOWER(internal_contributor) NOT LIKE '%server%' THEN 'General Purpose' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "cloud_resource_type", "colExpr": "COALESCE(cloud_resource_type,'No Data')", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "hardware_bios_manufacturer", "colExpr": "COALESCE(hardware_bios_manufacturer,'No Data')", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "hardware_chassis_type", "colExpr": "COALESCE(hardware_chassis_type,'No Data')", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "hardware_model", "colExpr": "COALESCE(hardware_model,'No Data')", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "edr_fully_functional", "colExpr": "COALESCE(edr_fully_functional,False)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "jail_broken", "colExpr": "COALESCE(jail_broken,False)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vm_tracking_method", "colExpr": "CASE WHEN size(vm_tracking_method) > 0 THEN vm_tracking_method ELSE array('No Data') END", "fieldsSpec": {"computationPhase": "inter"}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "location_country", "host_name", "fqdn", "ip", "netbios", "accessibility", "os", "cloud_provider", "cloud_resource_id", "hardware_model", "hardware_serial_number", "login_last_date", "login_last_user", "edr_onboarding_status", "vm_onboarding_status", "mdm_product", "mdm_status", "mdm_last_sync_date", "edr_last_scan_date", "vm_last_scan_date", "ad_distinguished_name", "ad_last_sync_date", "ad_operational_status", "aad_device_id", "aad_operational_status", "cloud_operational_state", "cloud_instance_lifecycle", "is_ephemeral", "mac_address", "vlan", "role"], "sourceSpecificProperties": [{"colName": "crowdstrike_product_type_desc", "colExpr": "COALESCE(crowdstrike_product_type_desc,'No Data')", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_compliance_status", "colExpr": "COALESCE(aad_compliance_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN aad_compliance_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_management_status", "colExpr": "COALESCE(aad_management_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN aad_management_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_has_iam_role", "colExpr": "COALESCE(aws_instance_has_iam_role, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN aws_instance_has_iam_role ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_content_update_status", "colExpr": "COALESCE(crowdstrike_content_update_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_content_update_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_device_control_status", "colExpr": "COALESCE(crowdstrike_device_control_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_device_control_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_fim_policy_status", "colExpr": "COALESCE(crowdstrike_fim_policy_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_fim_policy_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_firewall_status", "colExpr": "COALESCE(crowdstrike_firewall_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_firewall_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_global_config_status", "colExpr": "COALESCE(crowdstrike_global_config_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_global_config_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_host_retention_status", "colExpr": "COALESCE(crowdstrike_host_retention_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_host_retention_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_prevention_policy_status", "colExpr": "COALESCE(crowdstrike_prevention_policy_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_prevention_policy_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_remote_response_status", "colExpr": "COALESCE(crowdstrike_remote_response_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_remote_response_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_sensor_uninstall_protection_status", "colExpr": "COALESCE(crowdstrike_sensor_uninstall_protection_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_sensor_uninstall_protection_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_sensor_update_policy_status", "colExpr": "COALESCE(crowdstrike_sensor_update_policy_status, CASE WHEN array_contains(data_source_subset_name,'attribute_list') THEN crowdstrike_sensor_update_policy_status ELSE False END)", "fieldsSpec": {"computationPhase": "inter"}}]}