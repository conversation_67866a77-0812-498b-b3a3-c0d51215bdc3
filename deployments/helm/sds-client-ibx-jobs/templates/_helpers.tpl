{{/*
Expand the name of the chart.
*/}}
{{- define "sds-client-ibx-jobs.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "sds-client-ibx-jobs.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "sds-client-ibx-jobs.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "sds-client-ibx-jobs.labels" -}}
helm.sh/chart: {{ include "sds-client-ibx-jobs.chart" . }}
{{ include "sds-client-ibx-jobs.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "sds-client-ibx-jobs.selectorLabels" -}}
app.kubernetes.io/name: {{ include "sds-client-ibx-jobs.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "sds-client-ibx-jobs.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "sds-client-ibx-jobs.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}


{{/*
Expand the name of the chart.
*/}}
{{- define "k8jobs.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "k8jobs.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "k8jobs.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "k8jobs.labels" -}}
{{ include "k8jobs.selectorLabels" . }}
{{ include "k8jobs.jobLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "k8jobs.selectorLabels" -}}
app.kubernetes.io/name: {{ include "k8jobs.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{ include "k8pods.podLabels" . }}
{{- end }}

{{/*
  Generate labels for pods based on the values file.
*/}}
{{- define "k8pods.podLabels" -}}
{{- with .Values.podLabels }}
{{- toYaml . }}
{{- end }}
{{- end }}

{{/*
  Generate labels for k8jobs based on the values file.
*/}}
{{- define "k8jobs.jobLabels" -}}
{{- with .Values.jobLabels }}
{{- toYaml . }}
{{- end }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "k8jobs.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "k8jobs.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{- define "job_environment_from" }}
  {{- $Global := . }}
  {{- with .Values.jobEnvFrom }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "job_environment" }}
  {{- $Global := . }}
  {{- with .Values.jobEnv }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes_mounts" }}
  {{- $Global := . }}
  {{- with .Values.jobOverrides.sds_airflow_variable_update_job.volumeMounts }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes" }}
  {{- $Global := . }}
  {{- with .Values.jobOverrides.sds_airflow_variable_update_job.volumes }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "configs.getImageTag" -}}
{{- if index .Values "bundle-version" -}}
{{- $version := index .Values "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- .Values.config.image.tag -}}
{{- end -}}
{{- end -}}

{{- define "emdeploy.getImageTag" -}}
{{- if index .Values "jobOverrides" "em_api_config_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "em_api_config_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "em_api_config_deploy_job" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "dagdeploy.getImageTag" -}}
{{- if index .Values "jobOverrides" "sds_client_dag_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "sds_client_dag_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "sds_client_dag_deploy_job" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "airflowvariableupdate.getImageTag" -}}
{{- if index .Values "jobOverrides" "sds_airflow_variable_update_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "sds_airflow_variable_update_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "airflowsharedcopy.getImageTag" -}}
{{- if index .Values "jobOverrides" "sds_airflow_shared_copy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "sds_airflow_shared_copy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "camapiconfig.getImageTag" -}}
{{- if index .Values "jobOverrides" "cam_api_config_deploy" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "cam_api_config_deploy" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "ccmapiconfig.getImageTag" -}}
{{- if index .Values "jobOverrides" "sds_ccm_api_config_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "sds_ccm_api_config_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "eiconfigclient.getImageTag" -}}
{{- if index .Values "jobOverrides" "ei_config_client_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "ei_config_client_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "sparkconfigcopy.getImageTag" -}}
{{- if index .Values "jobOverrides" "spark_config_copy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "spark_config_copy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "documentdeploy.getImageTag" -}}
{{- if index .Values "jobOverrides" "document_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "document_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "cspiapiconfig.getImageTag" -}}
{{- if index .Values "jobOverrides" "cspi_api_config_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "cspi_api_config_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}

{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "idraapiconfig.getImageTag" -}}
{{- if index .Values "jobOverrides" "idra_api_config_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "idra_api_config_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{- define "vraapiconfig.getImageTag" -}}
{{- if index .Values "jobOverrides" "vra_api_config_deploy_job" "bundle-version" -}}
{{- $version := index .Values "jobOverrides" "vra_api_config_deploy_job" "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- index .Values "jobOverrides" "image" "tag" -}}
{{- end -}}
{{- end -}}

{{/* User defined Airflow variable job environment variables */}}
{{- define "custom_airflow_variable_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.sds_airflow_variable_update_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.sds_airflow_variable_update_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.sds_airflow_variable_update_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.sds_airflow_variable_update_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }}


{{/* User defined Airflow shared copy job environment variables */}}
{{- define "custom_airflow_shared_copy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.sds_airflow_shared_copy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.sds_airflow_shared_copy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.sds_airflow_shared_copy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.sds_airflow_shared_copy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }}

{{/* User defined CCM dag deploy job environment variables */}}
{{- define "custom_ccm_config_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.sds_ccm_api_config_deploy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.sds_ccm_api_config_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.sds_ccm_api_config_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.sds_ccm_api_config_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }}  


{{/* User defined CSPI config deploy job environment variables */}}
{{- define "custom_cspi_config_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.cspi_api_config_deploy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.cspi_api_config_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.cspi_api_config_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.cspi_api_config_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }}  


{{/* User defined EI config deploy job environment variables */}}
{{- define "custom_ei_config_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.ei_config_client_deploy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.ei_config_client_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.ei_config_client_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.ei_config_client_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }} 


{{/* User defined IDRA config deploy job environment variables */}}
{{- define "custom_idra_config_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.idra_api_config_deploy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.idra_api_config_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.idra_api_config_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.idra_api_config_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }} 


{{/* User defined NIFI shared copy job environment variables */}}
{{- define "custom_nifi_shared_copy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.sds_nifi_shared_copy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.sds_nifi_shared_copy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.sds_nifi_shared_copy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.sds_nifi_shared_copy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }} 


{{/* User defined Client dag deploy job environment variables */}}
{{- define "custom_client_dag_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.sds_client_dag_deploy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.sds_client_dag_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.sds_client_dag_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.sds_client_dag_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }} 

{{/* User defined Spark config copy environment variables */}}
{{- define "custom_spark_config_copy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.spark_config_copy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.spark_config_copy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.spark_config_copy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.spark_config_copy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }} 

{{/* User defined VRA config deploy job environment variables */}}
{{- define "custom_vra_config_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.vra_api_config_deploy_job.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.vra_api_config_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.vra_api_config_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.vra_api_config_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }}   

{{/* User defined Document deploy job environment variables */}}
{{- define "document_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.document_deploy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.document_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.document_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.document_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }}

{{/* User defined EM api config deploy job environment variables */}}
{{- define "custom_em_api_config_deploy_job_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.jobOverrides.em_api_config_deploy_job.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.jobOverrides.em_api_config_deploy_job.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.jobOverrides.em_api_config_deploy_job.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.jobOverrides.em_api_config_deploy_job.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
  {{- end }}