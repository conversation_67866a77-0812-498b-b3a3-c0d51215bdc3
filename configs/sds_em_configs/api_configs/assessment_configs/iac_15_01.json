{"id": "IAC-15-01", "title": "Devices in Azure AD registered in MDM are managed with an MDM application", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment ensures that Azure AD-registered devices with MDM service configured are properly managed by an MDM application.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active devices from Host where Origin contains MS Azure AD and AAD Management Service equals MDM", "Validate that 'AAD Management Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,aad_management_service,origin,origin as affected_asset_origin,aad_management_status from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where  activity_status='Active' and ARRAY_CONTAINS(origin,'MS Azure AD') and aad_management_service='MDM'", "success_condition": "aad_management_status=true", "finding_primary_key": "p_id", "finding_title": "MDM Management Not Enabled", "finding_config": [{"title": "AAD Management Status", "expression": "aad_management_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["IAC-15"]}}