kg_ui_sanity_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: vibin_wilson
    retries: 1
    start_date: 2024-01-01
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      run_prerequisites: false
      generate_report: true
      report_base_path: "<%QA_REPORT_URI%>/behave/"
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        TEST_ENV: "qa"
        DEBUG_MODE: "true"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        ICEBERG_WAREHOUSE: "<%DATALAKE_URI%>/iceberg/"
        ICEBERG_CATALOG_TYPE: "<%ICEBERG_CATALOG_TYPE%>"
        ICEBERG_CATALOG_URI: "<%ICEBERG_CATALOG_URI%>"
        HADOOP_FS_IMPL: "<%HADOOP_FS_IMPL%>"
        HADOOP_FS_END_POINT: "<%HADOOP_FS_ENDPOINT%>"
        SERVICE_ACCOUNT: "<%SERVICE_ACCOUNT%>"
        IMAGE: "prevalentai/sds-product-em-qa-automation:<%EM_QA_IMAGE_VERSION%>"
        SDM_SCHEMA_NAME: "<%SDM_SCHEMA_NAME%>"
        EI_SCHEMA_NAME: "<%EI_SCHEMA_NAME%>"
        SRDM_SCHEMA_NAME: "<%SRDM_SCHEMA_NAME%>"
        EXECUTOR_MEMORY: "6g"
        DRIVER_MEMORY: "4g"
        EXECUTOR_INSTANCES: "4"
        EXECUTOR_CORES: "1"
        APP_NAME: "EM"
        UPDATED_AT: "{{data_interval_end.int_timestamp}}"
        DOMAIN_URL: "{{macros.custom_macros.get_env('DOMAIN_URL')}}"
        SELENIUM_SDS3_URL: "{{macros.custom_macros.get_env('SELENIUM_SDS3_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        AIRFLOW_CONN_ADMIN_API_BASE_URL: "{{macros.custom_macros.get_env('AIRFLOW_CONN_ADMIN_API_BASE_URL')}}"
        AIRFLOW_CONN_RCM_ADMIN_API: "{{macros.custom_macros.get_env('AIRFLOW_CONN_RCM_ADMIN_API')}}"
        MANAGEMENT_API_UI_MAPPING_ENDPOINT: "{{macros.custom_macros.get_env('MANAGEMENT_API_UI_MAPPING_ENDPOINT')}}"
        DATALAKE_URI: "s3a://{{macros.custom_macros.get_env('S3_DATALAKE_BUCKET_NAME')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        KEYCLOAK_SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
      secrets:
      - deploy_type: env
        deploy_target: SELENIUM_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumUsername
      - deploy_type: env
        deploy_target: SELENIUM_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumPassword
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: GIT_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: gitPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    UI_test:
      image: prevalentai/sds-product-em-qa-automation-ui:<%EM_QA_IMAGE_VERSION%>
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      dependencies: [start_task]
      tags: ["sanity-test"]
      feature_files: ['src/knowledge_graph/ui_test/features/KG_UI.feature']
    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [UI_test]
