{"id": "VPM-05-02", "title": "Devices should not have end of life or obsolete software reported via VM tools", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment checks whether devices are running outdated or unsupported software, which can increase security and compliance risks. Keeping software up to date enhances system stability, protects against exploits, and ensures continued vendor support.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Server and Workstation from Host", "Use 'Host Has Vulnerability Finding' relationship from Graph filter where finding status is Open", "Validate that vulnerabilities with title like 'EOL/Obsolete Software'"], "scope_query": "SELECT host.p_id AS host_p_id, ARRAY(STRUCT(host.p_id, host.class, host.display_label, host.activity_status)) AS affected_asset, host.origin AS affected_asset_origin, collect_set(reln.current_status) AS vuln_status, SIZE(collect_set(vuln.title)) AS obsolete_software_count FROM <%EI_SCHEMA_NAME%>.sds_ei__host__enrich AS host LEFT JOIN <%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host__enrich AS reln ON host.p_id = reln.target_p_id AND reln.current_status != 'Closed' LEFT JOIN (SELECT p_id, title FROM <%EI_SCHEMA_NAME%>.sds_ei__vulnerability__enrich WHERE LOWER(title) LIKE '%eol/obsolete software%') AS vuln ON reln.source_p_id = vuln.p_id WHERE host.activity_status = 'Active' AND (host.type = 'Server' OR host.type = 'Workstation') GROUP BY host.p_id, host.class, host.display_label, host.activity_status, host.origin, host.os_family", "success_condition": "obsolete_software_count=0", "finding_primary_key": "host_p_id", "finding_title": "End-of-Life or Obsolete Software Detected", "finding_config": [{"title": "Obsolete Software Count", "expression": "obsolete_software_count", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["VPM-05"]}}