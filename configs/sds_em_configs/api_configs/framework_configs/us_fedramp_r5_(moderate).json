{"framework_name": "US FedRAMP R5 (moderate)", "framework_code": "fedramp_r5_(moderate)", "number_of_levels": 2, "component_types": [{"component_level": 1, "component_type_code": "family", "component_type_name": "Family", "component_type_description": ""}, {"component_level": 2, "component_type_code": "control", "component_type_name": "Control", "component_type_description": ""}], "components": [{"component_id": "ac", "component_code": "AC", "component_name": "ACCESS CONTROL", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "ac-1", "component_code": "AC-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2", "component_code": "AC-2", "component_name": "Account Management", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(1)", "component_code": "AC-2(1)", "component_name": "Account Management | Automated System Account Management", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(2)", "component_code": "AC-2(2)", "component_name": "Account Management | Automated Temporary and Emergency Account Management", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(3)", "component_code": "AC-2(3)", "component_name": "Account Management | Disable Accounts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(4)", "component_code": "AC-2(4)", "component_name": "Account Management | Automated Audit Actions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(5)", "component_code": "AC-2(5)", "component_name": "Account Management | Inactivity Logout", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(7)", "component_code": "AC-2(7)", "component_name": "Account Management | Privileged User Accounts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(9)", "component_code": "AC-2(9)", "component_name": "Account Management | Restrictions on Use of Shared and Group Accounts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(12)", "component_code": "AC-2(12)", "component_name": "Account Management | Account Monitoring for Atypical Usage", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-2(13)", "component_code": "AC-2(13)", "component_name": "Account Management | Disable Accounts for High-risk Individuals", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-3", "component_code": "AC-3", "component_name": "Access Enforcement", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-4", "component_code": "AC-4", "component_name": "Information Flow Enforcement", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-4(21)", "component_code": "AC-4(21)", "component_name": "Information Flow Enforcement | Physical or Logical Separation of Information Flows", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-5", "component_code": "AC-5", "component_name": "Separation of Duties", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-6", "component_code": "AC-6", "component_name": "Least Privilege", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-6(1)", "component_code": "AC-6(1)", "component_name": "Least Privilege | Authorize Access to Security Functions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-6(2)", "component_code": "AC-6(2)", "component_name": "Least Privilege | Non-privileged Access for Nonsecurity Functions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-6(5)", "component_code": "AC-6(5)", "component_name": "Least Privilege | Privileged Accounts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-6(7)", "component_code": "AC-6(7)", "component_name": "Least Privilege | Review of User Privileges", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-6(9)", "component_code": "AC-6(9)", "component_name": "Least Privilege | Log Use of Privileged Functions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-6(10)", "component_code": "AC-6(10)", "component_name": "Least Privilege | Prohibit Non-privileged Users from Executing Privileged Functions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-7", "component_code": "AC-7", "component_name": "Unsuccessful Logon Attempts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-8", "component_code": "AC-8", "component_name": "System Use Notification", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-11", "component_code": "AC-11", "component_name": "Device Lock", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-11(1)", "component_code": "AC-11(1)", "component_name": "Device Lock | Pattern-hiding Displays", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-12", "component_code": "AC-12", "component_name": "Session Termination", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-14", "component_code": "AC-14", "component_name": "Permitted Actions Without Identification or Authentication", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-17", "component_code": "AC-17", "component_name": "Remote Access", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-17(1)", "component_code": "AC-17(1)", "component_name": "Remote Access | Monitoring and Control", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-17(2)", "component_code": "AC-17(2)", "component_name": "Remote Access | Protection of Confidentiality and Integrity Using Encryption", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-17(3)", "component_code": "AC-17(3)", "component_name": "Remote Access | Managed Access Control Points", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-17(4)", "component_code": "AC-17(4)", "component_name": "Remote Access | Privileged Commands and Access", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-18", "component_code": "AC-18", "component_name": "Wireless Access", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-18(1)", "component_code": "AC-18(1)", "component_name": "Wireless Access | Authentication and Encryption", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-18(3)", "component_code": "AC-18(3)", "component_name": "Wireless Access | Disable Wireless Networking", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-19", "component_code": "AC-19", "component_name": "Access Control for Mobile Devices", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-19(5)", "component_code": "AC-19(5)", "component_name": "Access Control for Mobile Devices | Full Device or Container-based Encryption", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-20", "component_code": "AC-20", "component_name": "Use of External Systems", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-20(1)", "component_code": "AC-20(1)", "component_name": "Use of External Systems | Limits on Authorized Use", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-20(2)", "component_code": "AC-20(2)", "component_name": "Use of External Systems | Portable Storage Devices — Restricted Use", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-21", "component_code": "AC-21", "component_name": "Information Sharing", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ac-22", "component_code": "AC-22", "component_name": "Publicly Accessible Content", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "aat", "component_code": "AAT", "component_name": "AWARENESS AND TRAINING", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "at-1", "component_code": "AT-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "at-2", "component_code": "AT-2", "component_name": "Literacy Training and Awareness", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "at-2(2)", "component_code": "AT-2(2)", "component_name": "Literacy Training and Awareness | Insider Threat", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "at-2(3)", "component_code": "AT-2(3)", "component_name": "Literacy Training and Awareness | Social Engineering and Mining", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "at-3", "component_code": "AT-3", "component_name": "Role-based Training", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "at-4", "component_code": "AT-4", "component_name": "Training Records", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "aaa", "component_code": "AAA", "component_name": "AUDIT AND ACCOUNTABILITY", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "au-1", "component_code": "AU-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-2", "component_code": "AU-2", "component_name": "Event Logging", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-3", "component_code": "AU-3", "component_name": "Content of Audit Records", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-3(1)", "component_code": "AU-3(1)", "component_name": "Content of Audit Records | Additional Audit Information", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-4", "component_code": "AU-4", "component_name": "Audit Log Storage Capacity", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-5", "component_code": "AU-5", "component_name": "Response to Audit Logging Process Failures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-6", "component_code": "AU-6", "component_name": "Audit Record Review, Analysis, and Reporting", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-6(1)", "component_code": "AU-6(1)", "component_name": "Audit Record Review, Analysis, and Reporting | Automated Process Integration", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-6(3)", "component_code": "AU-6(3)", "component_name": "Audit Record Review, Analysis, and Reporting | Correlate Audit Record Repositories", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-7", "component_code": "AU-7", "component_name": "Audit Record Reduction and Report Generation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-7(1)", "component_code": "AU-7(1)", "component_name": "Audit Record Reduction and Report Generation | Automatic Processing", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-8", "component_code": "AU-8", "component_name": "Time Stamps", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-9", "component_code": "AU-9", "component_name": "Protection of Audit Information", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-9(4)", "component_code": "AU-9(4)", "component_name": "Protection of Audit Information | Access by Subset of Privileged Users", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-11", "component_code": "AU-11", "component_name": "Audit Record Retention", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "au-12", "component_code": "AU-12", "component_name": "Audit Record Generation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "saaa", "component_code": "SAAA", "component_name": "SECURITY ASSESSMENT AND AUTHORIZ<PERSON>ION", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "ca-1", "component_code": "CA-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-2", "component_code": "CA-2", "component_name": "Control Assessments", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-2(1)", "component_code": "CA-2(1)", "component_name": "Control Assessments | Independent Assessors", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-2(3)", "component_code": "CA-2(3)", "component_name": "Control Assessments | Leveraging Results from External Organizations", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-3", "component_code": "CA-3", "component_name": "Information Exchange", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-5", "component_code": "CA-5", "component_name": "Plan of Action and Milestones", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-6", "component_code": "CA-6", "component_name": "Authorization", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-7", "component_code": "CA-7", "component_name": "Continuous Monitoring", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-7(1)", "component_code": "CA-7(1)", "component_name": "Continuous Monitoring | Independent Assessment", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-7(4)", "component_code": "CA-7(4)", "component_name": "Continuous Monitoring | Risk Monitoring", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-8", "component_code": "CA-8", "component_name": "Penetration Testing", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-8(1)", "component_code": "CA-8(1)", "component_name": "Penetration Testing | Independent Penetration Testing Agent or Team", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-8(2)", "component_code": "CA-8(2)", "component_name": "Penetration Testing | Red Team Exercises", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ca-9", "component_code": "CA-9", "component_name": "Internal System Connections", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "cm", "component_code": "CM", "component_name": "CONFIGURATION MANAGEMENT", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "cm-1", "component_code": "CM-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-2", "component_code": "CM-2", "component_name": "Baseline Configuration", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-2(2)", "component_code": "CM-2(2)", "component_name": "Baseline Configuration | Automation Support for Accuracy and Currency", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-2(3)", "component_code": "CM-2(3)", "component_name": "Baseline Configuration | Retention of Previous Configurations", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-2(7)", "component_code": "CM-2(7)", "component_name": "Baseline Configuration | Configure Systems and Components for High-risk Areas", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-3", "component_code": "CM-3", "component_name": "Configuration Change Control", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-3(2)", "component_code": "CM-3(2)", "component_name": "Configuration Change Control | Testing, Validation, and Documentation of Changes", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-3(4)", "component_code": "CM-3(4)", "component_name": "Configuration Change Control | Security and Privacy Representatives", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-4", "component_code": "CM-4", "component_name": "Impact Analyses", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-4(2)", "component_code": "CM-4(2)", "component_name": "Impact Analyses | Verification of Controls", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-5", "component_code": "CM-5", "component_name": "Access Restrictions for Change", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-5(1)", "component_code": "CM-5(1)", "component_name": "Access Restrictions for Change | Automated Access Enforcement and Audit Records", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-5(5)", "component_code": "CM-5(5)", "component_name": "Access Restrictions for Change | Privilege Limitation for Production and Operation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-6", "component_code": "CM-6", "component_name": "Configuration Settings", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-6(1)", "component_code": "CM-6(1)", "component_name": "Configuration Settings | Automated Management, Application, and Verification", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-7", "component_code": "CM-7", "component_name": "Least Functionality", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-7(1)", "component_code": "CM-7(1)", "component_name": "Least Functionality | Periodic Review", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-7(2)", "component_code": "CM-7(2)", "component_name": "Least Functionality | Prevent Program Execution", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-7(5)", "component_code": "CM-7(5)", "component_name": "Least Functionality | Authorized Software — Allow-by-exception", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-8", "component_code": "CM-8", "component_name": "System Component Inventory", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-8(1)", "component_code": "CM-8(1)", "component_name": "System Component Inventory | Updates During Installation and Removal", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-8(3)", "component_code": "CM-8(3)", "component_name": "System Component Inventory | Automated Unauthorized Component Detection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-9", "component_code": "CM-9", "component_name": "Configuration Management Plan", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-10", "component_code": "CM-10", "component_name": "Software Usage Restrictions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-11", "component_code": "CM-11", "component_name": "User-installed Software", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-12", "component_code": "CM-12", "component_name": "Information Location", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cm-12(1)", "component_code": "CM-12(1)", "component_name": "Information Location | Automated Tools to Support Information Location", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "cp", "component_code": "CP", "component_name": "CONTINGENCY PLANNING", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "cp-1", "component_code": "CP-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-2", "component_code": "CP-2", "component_name": "Contingency Plan", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-2(1)", "component_code": "CP-2(1)", "component_name": "Contingency Plan | Coordinate with Related Plans", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-2(3)", "component_code": "CP-2(3)", "component_name": "Contingency Plan | Resume Mission and Business Functions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-2(8)", "component_code": "CP-2(8)", "component_name": "Contingency Plan | Identify Critical Assets", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-3", "component_code": "CP-3", "component_name": "Contingency Training", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-4", "component_code": "CP-4", "component_name": "Contingency Plan Testing", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-4(1)", "component_code": "CP-4(1)", "component_name": "Contingency Plan Testing | Coordinate with Related Plans", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-6", "component_code": "CP-6", "component_name": "Alternate Storage Site", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-6(1)", "component_code": "CP-6(1)", "component_name": "Alternate Storage Site | Separation from Primary Site", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-6(3)", "component_code": "CP-6(3)", "component_name": "Alternate Storage Site | Accessibility", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-7", "component_code": "CP-7", "component_name": "Alternate Processing Site", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-7(1)", "component_code": "CP-7(1)", "component_name": "Alternate Processing Site | Separation from Primary Site", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-7(2)", "component_code": "CP-7(2)", "component_name": "Alternate Processing Site | Accessibility", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-7(3)", "component_code": "CP-7(3)", "component_name": "Alternate Processing Site | Priority of Service", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-8", "component_code": "CP-8", "component_name": "Telecommunications Services", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-8(1)", "component_code": "CP-8(1)", "component_name": "Telecommunications Services | Priority of Service Provisions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-8(2)", "component_code": "CP-8(2)", "component_name": "Telecommunications Services | Single Points of Failure", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-9", "component_code": "CP-9", "component_name": "System Backup", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-9(1)", "component_code": "CP-9(1)", "component_name": "System Backup | Testing for Reliability and Integrity", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-9(8)", "component_code": "CP-9(8)", "component_name": "System Backup | Cryptographic Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-10", "component_code": "CP-10", "component_name": "System Recovery and Reconstitution", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "cp-10(2)", "component_code": "CP-10(2)", "component_name": "System Recovery and Reconstitution | Transaction Recovery", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "iaa", "component_code": "IAA", "component_name": "IDENTIFICATION AND AUTHENTICATION", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "ia-1", "component_code": "IA-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-2", "component_code": "IA-2", "component_name": "Identification and Authentication (organizational Users)", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-2(1)", "component_code": "IA-2(1)", "component_name": "Identification and Authentication (organizational Users) | Multi-factor Authentication to Privileged Accounts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-2(2)", "component_code": "IA-2(2)", "component_name": "Identification and Authentication (organizational Users) | Multi-factor Authentication to Non-privileged Accounts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-2(5)", "component_code": "IA-2(5)", "component_name": "Identification and Authentication (organizational Users) | Individual Authentication with Group Authentication", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-2(6)", "component_code": "IA-2(6)", "component_name": "Identification and Authentication (organizational Users) | Access to Accounts —separate Device", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-2(8)", "component_code": "IA-2(8)", "component_name": "Identification and Authentication (organizational Users) | Access to Accounts — Replay Resistant", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-2(12)", "component_code": "IA-2(12)", "component_name": "Identification and Authentication (organizational Users) | Acceptance of PIV Credentials", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-3", "component_code": "IA-3", "component_name": "Device Identification and Authentication", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-4", "component_code": "IA-4", "component_name": "Identifier Management", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-4(4)", "component_code": "IA-4(4)", "component_name": "Identifier Management | Identify User Status", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-5", "component_code": "IA-5", "component_name": "Authenticator Management", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-5(1)", "component_code": "IA-5(1)", "component_name": "Authenticator Management | Password-based Authentication", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-5(2)", "component_code": "IA-5(2)", "component_name": "Authenticator Management | Public Key-based Authentication", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-5(6)", "component_code": "IA-5(6)", "component_name": "Authenticator Management | Protection of Authenticators", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-5(7)", "component_code": "IA-5(7)", "component_name": "Authenticator Management | No Embedded Unencrypted Static Authenticators", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-6", "component_code": "IA-6", "component_name": "Authentication Feedback", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-7", "component_code": "IA-7", "component_name": "Cryptographic Module Authentication", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-8", "component_code": "IA-8", "component_name": "Identification and Authentication (non-organizational Users)", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-8(1)", "component_code": "IA-8(1)", "component_name": "Identification and Authentication (non-organizational Users) | Acceptance of PIV Credentials from Other Agencies", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-8(2)", "component_code": "IA-8(2)", "component_name": "Identification and Authentication (non-organizational Users) | Acceptance of External Authenticators", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-8(4)", "component_code": "IA-8(4)", "component_name": "Identification and Authentication (non-organizational Users) | Use of Defined Profiles", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-11", "component_code": "IA-11", "component_name": "Re-authentication", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-12", "component_code": "IA-12", "component_name": "Identity Proofing", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-12(2)", "component_code": "IA-12(2)", "component_name": "Identity Proofing | Identity Evidence", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-12(3)", "component_code": "IA-12(3)", "component_name": "Identity Proofing | Identity Evidence Validation and Verification", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ia-12(5)", "component_code": "IA-12(5)", "component_name": "Identity Proofing | Address Confirmation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "ir", "component_code": "IR", "component_name": "INCIDENT RESPONSE", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "ir-1", "component_code": "IR-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-2", "component_code": "IR-2", "component_name": "Incident Response Training", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-3", "component_code": "IR-3", "component_name": "Incident Response Testing", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-3(2)", "component_code": "IR-3(2)", "component_name": "Incident Response Testing | Coordination with Related Plans", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-4", "component_code": "IR-4", "component_name": "Incident Handling", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-4(1)", "component_code": "IR-4(1)", "component_name": "Incident Handling | Automated Incident Handling Processes", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-5", "component_code": "IR-5", "component_name": "Incident Monitoring", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-6", "component_code": "IR-6", "component_name": "Incident Reporting", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-6(1)", "component_code": "IR-6(1)", "component_name": "Incident Reporting | Automated Reporting", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-6(3)", "component_code": "IR-6(3)", "component_name": "Incident Reporting | Supply Chain Coordination", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-7", "component_code": "IR-7", "component_name": "Incident Response Assistance", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-7(1)", "component_code": "IR-7(1)", "component_name": "Incident Response Assistance | Automation Support for Availability of Information and Support", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-8", "component_code": "IR-8", "component_name": "Incident Response Plan", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-9", "component_code": "IR-9", "component_name": "Information Spillage Response", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-9(2)", "component_code": "IR-9(2)", "component_name": "Information Spillage Response | Training", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-9(3)", "component_code": "IR-9(3)", "component_name": "Information Spillage Response | Post-spill Operations", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ir-9(4)", "component_code": "IR-9(4)", "component_name": "Information Spillage Response | Exposure to Unauthorized Personnel", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "m", "component_code": "M", "component_name": "MAINTENANCE", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "ma-1", "component_code": "MA-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-2", "component_code": "MA-2", "component_name": "Controlled Maintenance", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-3", "component_code": "MA-3", "component_name": "Maintenance Tools", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-3(1)", "component_code": "MA-3(1)", "component_name": "Maintenance Tools | Inspect Tools", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-3(2)", "component_code": "MA-3(2)", "component_name": "Maintenance Tools | Inspect Media", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-3(3)", "component_code": "MA-3(3)", "component_name": "Maintenance Tools | Prevent Unauthorized Removal", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-4", "component_code": "MA-4", "component_name": "Nonlocal Maintenance", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-5", "component_code": "MA-5", "component_name": "Maintenance Personnel", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-5(1)", "component_code": "MA-5(1)", "component_name": "Maintenance Personnel | Individuals Without Appropriate Access", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ma-6", "component_code": "MA-6", "component_name": "Timely Maintenance", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "mp", "component_code": "MP", "component_name": "MEDIA PROTECTION", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "mp-1", "component_code": "MP-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "mp-2", "component_code": "MP-2", "component_name": "Media Access", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "mp-3", "component_code": "MP-3", "component_name": "Media Marking", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "mp-4", "component_code": "MP-4", "component_name": "Media Storage", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "mp-5", "component_code": "MP-5", "component_name": "Media Transport", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "mp-6", "component_code": "MP-6", "component_name": "Media Sanitization", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "mp-7", "component_code": "MP-7", "component_name": "Media Use", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "paep", "component_code": "PAEP", "component_name": "PHYSICAL AND ENVIRONMENTAL PROTECTION", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "pe-1", "component_code": "PE-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-2", "component_code": "PE-2", "component_name": "Physical Access Authorizations", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-3", "component_code": "PE-3", "component_name": "Physical Access Control", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-4", "component_code": "PE-4", "component_name": "Access Control for Transmission", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-5", "component_code": "PE-5", "component_name": "Access Control for Output Devices", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-6", "component_code": "PE-6", "component_name": "Monitoring Physical Access", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-6(1)", "component_code": "PE-6(1)", "component_name": "Monitoring Physical Access | Intrusion Alarms and Surveillance Equipment", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-8", "component_code": "PE-8", "component_name": "Visitor Access Records", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-9", "component_code": "PE-9", "component_name": "Power Equipment and Cabling", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-10", "component_code": "PE-10", "component_name": "Emergency Shutoff", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-11", "component_code": "PE-11", "component_name": "Emergency Power", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-12", "component_code": "PE-12", "component_name": "Emergency Lighting", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-13", "component_code": "PE-13", "component_name": "Fire Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-13(1)", "component_code": "PE-13(1)", "component_name": "Fire Protection | Detection Systems — Automatic Activation and Notification", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-13(2)", "component_code": "PE-13(2)", "component_name": "Fire Protection | Suppression Systems — Automatic Activation and Notification", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-14", "component_code": "PE-14", "component_name": "Environmental Controls", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-15", "component_code": "PE-15", "component_name": "Water Damage Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-16", "component_code": "PE-16", "component_name": "Delivery and Removal", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pe-17", "component_code": "PE-17", "component_name": "Alternate Work Site", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "p", "component_code": "P", "component_name": "PLANNING", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "pl-1", "component_code": "PL-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pl-2", "component_code": "PL-2", "component_name": "System Security and Privacy Plans", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pl-4", "component_code": "PL-4", "component_name": "Rules of Behavior", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pl-4(1)", "component_code": "PL-4(1)", "component_name": "Rules of Behavior | Social Media and External Site/application Usage Restrictions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pl-8", "component_code": "PL-8", "component_name": "Security and Privacy Architectures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pl-10", "component_code": "PL-10", "component_name": "Baseline Selection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "pl-11", "component_code": "PL-11", "component_name": "Baseline Tailoring", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "ps", "component_code": "PS", "component_name": "PERSONNEL SECURITY", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "ps-1", "component_code": "PS-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-2", "component_code": "PS-2", "component_name": "Position Risk Designation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-3", "component_code": "PS-3", "component_name": "Personnel Screening", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-3(3)", "component_code": "PS-3(3)", "component_name": "Personnel Screening | Information Requiring Special Protective Measures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-4", "component_code": "PS-4", "component_name": "Personnel Termination", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-5", "component_code": "PS-5", "component_name": "Personnel Transfer", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-6", "component_code": "PS-6", "component_name": "Access Agreements", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-7", "component_code": "PS-7", "component_name": "External Personnel Security", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-8", "component_code": "PS-8", "component_name": "Personnel Sanctions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ps-9", "component_code": "PS-9", "component_name": "Position Descriptions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "ra", "component_code": "RA", "component_name": "RISK ASSESSMENT", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "ra-1", "component_code": "RA-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-2", "component_code": "RA-2", "component_name": "Security Categorization", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-3", "component_code": "RA-3", "component_name": "Risk Assessment", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-3(1)", "component_code": "RA-3(1)", "component_name": "Risk Assessment | Supply Chain Risk Assessment", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-5", "component_code": "RA-5", "component_name": "Vulnerability Monitoring and Scanning", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-5(2)", "component_code": "RA-5(2)", "component_name": "Vulnerability Monitoring and Scanning | Update Vulnerabilities to Be Scanned", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-5(3)", "component_code": "RA-5(3)", "component_name": "Vulnerability Monitoring and Scanning | Breadth and Depth of Coverage", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-5(5)", "component_code": "RA-5(5)", "component_name": "Vulnerability Monitoring and Scanning | Privileged Access", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-5(11)", "component_code": "RA-5(11)", "component_name": "Vulnerability Monitoring and Scanning | Public Disclosure Program", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-7", "component_code": "RA-7", "component_name": "Risk Response", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "ra-9", "component_code": "RA-9", "component_name": "Criticality Analysis", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "sasa", "component_code": "SASA", "component_name": "SYSTEM AND SERVICES ACQUISITION", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "sa-1", "component_code": "SA-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-2", "component_code": "SA-2", "component_name": "Allocation of Resources", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-3", "component_code": "SA-3", "component_name": "System Development Life Cycle", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-4", "component_code": "SA-4", "component_name": "Acquisition Process", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-4(1)", "component_code": "SA-4(1)", "component_name": "Acquisition Process | Functional Properties of Controls", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-4(2)", "component_code": "SA-4(2)", "component_name": "Acquisition Process | Design and Implementation Information for Controls", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-4(9)", "component_code": "SA-4(9)", "component_name": "Acquisition Process | Functions, Ports, Protocols, and Services in Use", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-4(10)", "component_code": "SA-4(10)", "component_name": "Acquisition Process | Use of Approved PIV Products", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-5", "component_code": "SA-5", "component_name": "System Documentation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-8", "component_code": "SA-8", "component_name": "Security and Privacy Engineering Principles", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-9", "component_code": "SA-9", "component_name": "External System Services", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-9(1)", "component_code": "SA-9(1)", "component_name": "External System Services | Risk Assessments and Organizational Approvals", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-9(2)", "component_code": "SA-9(2)", "component_name": "External System Services | Identification of Functions, Ports, Protocols, and Services", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-9(5)", "component_code": "SA-9(5)", "component_name": "External System Services | Processing, Storage, and Service Location", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-10", "component_code": "SA-10", "component_name": "Developer Configuration Management", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-11", "component_code": "SA-11", "component_name": "Developer Testing and Evaluation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-11(1)", "component_code": "SA-11(1)", "component_name": "Developer Testing and Evaluation | Static Code Analysis", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-11(2)", "component_code": "SA-11(2)", "component_name": "Developer Testing and Evaluation | Threat Modeling and Vulnerability Analyses", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-15", "component_code": "SA-15", "component_name": "Development Process, Standards, and Tools", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-15(3)", "component_code": "SA-15(3)", "component_name": "Development Process, Standards, and Tools | Criticality Analysis", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sa-22", "component_code": "SA-22", "component_name": "Unsupported System Components", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "sacp", "component_code": "SACP", "component_name": "SYSTEM AND COMMUNICATIONS PROTECTION", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "sc-1", "component_code": "SC-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-2", "component_code": "SC-2", "component_name": "Separation of System and User Functionality", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-4", "component_code": "SC-4", "component_name": "Information in Shared System Resources", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-5", "component_code": "SC-5", "component_name": "Denial-of-service Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7", "component_code": "SC-7", "component_name": "Boundary Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7(3)", "component_code": "SC-7(3)", "component_name": "Boundary Protection | Access Points", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7(4)", "component_code": "SC-7(4)", "component_name": "Boundary Protection | External Telecommunications Services", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7(5)", "component_code": "SC-7(5)", "component_name": "Boundary Protection | Deny by De<PERSON>ult — Allow by Exception", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7(7)", "component_code": "SC-7(7)", "component_name": "Boundary Protection | Split Tunneling for Remote Devices", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7(8)", "component_code": "SC-7(8)", "component_name": "Boundary Protection | Route Traffic to Authenticated Proxy Servers", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7(12)", "component_code": "SC-7(12)", "component_name": "Boundary Protection | Host-based Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-7(18)", "component_code": "SC-7(18)", "component_name": "Boundary Protection | Fail Secure", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-8", "component_code": "SC-8", "component_name": "Transmission Confidentiality and Integrity", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-8(1)", "component_code": "SC-8(1)", "component_name": "Transmission Confidentiality and Integrity | Cryptographic Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-10", "component_code": "SC-10", "component_name": "Network Disconnect", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-12", "component_code": "SC-12", "component_name": "Cryptographic Key Establishment and Management", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-13", "component_code": "SC-13", "component_name": "Cryptographic Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-15", "component_code": "SC-15", "component_name": "Collaborative Computing Devices and Applications", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-17", "component_code": "SC-17", "component_name": "Public Key Infrastructure Certificates", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-18", "component_code": "SC-18", "component_name": "Mobile Code", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-20", "component_code": "SC-20", "component_name": "Secure Name/address Resolution Service (authoritative Source)", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-21", "component_code": "SC-21", "component_name": "Secure Name/address Resolution Service (recursive or Caching Resolver)", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-22", "component_code": "SC-22", "component_name": "Architecture and Provisioning for Name/address Resolution Service", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-23", "component_code": "SC-23", "component_name": "Session Authenticity", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-28", "component_code": "SC-28", "component_name": "Protection of Information at Rest", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-28(1)", "component_code": "SC-28(1)", "component_name": "Protection of Information at Rest | Cryptographic Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-39", "component_code": "SC-39", "component_name": "Process Isolation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-45", "component_code": "SC-45", "component_name": "System Time Synchronization", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sc-45(1)", "component_code": "SC-45(1)", "component_name": "System Time Synchronization | Synchronization with Authoritative Time Source", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "saii", "component_code": "SAII", "component_name": "SYSTEM AND INFORMATION INTEGRITY", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "si-1", "component_code": "SI-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-2", "component_code": "SI-2", "component_name": "Flaw Remediation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-2(2)", "component_code": "SI-2(2)", "component_name": "Flaw Remediation | Automated Flaw Remediation Status", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-2(3)", "component_code": "SI-2(3)", "component_name": "Flaw Remediation | Time to Remediate Flaws and Benchmarks for Corrective Actions", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-3", "component_code": "SI-3", "component_name": "Malicious Code Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4", "component_code": "SI-4", "component_name": "System Monitoring", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4(1)", "component_code": "SI-4(1)", "component_name": "System Monitoring | System-wide Intrusion Detection System", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4(2)", "component_code": "SI-4(2)", "component_name": "System Monitoring | Automated Tools and Mechanisms for Real-time Analysis", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4(4)", "component_code": "SI-4(4)", "component_name": "System Monitoring | Inbound and Outbound Communications Traffic", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4(5)", "component_code": "SI-4(5)", "component_name": "System Monitoring | System-generated Alerts", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4(16)", "component_code": "SI-4(16)", "component_name": "System Monitoring | Correlate Monitoring Information", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4(18)", "component_code": "SI-4(18)", "component_name": "System Monitoring | Analyze Traffic and Covert Exfiltration", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-4(23)", "component_code": "SI-4(23)", "component_name": "System Monitoring | Host-based Devices", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-5", "component_code": "SI-5", "component_name": "Security Alerts, Advisories, and Directives", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-6", "component_code": "SI-6", "component_name": "Security and Privacy Function Verification", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-7", "component_code": "SI-7", "component_name": "Software, Firmware, and Information Integrity", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-7(1)", "component_code": "SI-7(1)", "component_name": "Software, Firmware, and Information Integrity | Integrity Checks", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-7(7)", "component_code": "SI-7(7)", "component_name": "Software, Firmware, and Information Integrity | Integration of Detection and Response", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-8", "component_code": "SI-8", "component_name": "Spam Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-8(2)", "component_code": "SI-8(2)", "component_name": "Spam Protection | Automatic Updates", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-10", "component_code": "SI-10", "component_name": "Information Input Validation", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-11", "component_code": "SI-11", "component_name": "Erro<PERSON>", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-12", "component_code": "SI-12", "component_name": "Information Management and Retention", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "si-16", "component_code": "SI-16", "component_name": "Memory Protection", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}, {"component_id": "scrmf", "component_code": "SCRMF", "component_name": "SUPPLY CHAIN RISK MANAGEMENT FAMILY", "component_type": "Family", "component_level": 1, "component_type_code": "family", "component_weightage": 1, "component_description": "", "components": [{"component_id": "sr-1", "component_code": "SR-1", "component_name": "Policy and Procedures", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-2", "component_code": "SR-2", "component_name": "Supply Chain Risk Management Plan", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-2(1)", "component_code": "SR-2(1)", "component_name": "Supply Chain Risk Management Plan | Establish SCRM Team", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-3", "component_code": "SR-3", "component_name": "Supply Chain Controls and Processes", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-5", "component_code": "SR-5", "component_name": "Acquisition Strategies, Tools, and Methods", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-6", "component_code": "SR-6", "component_name": "Supplier Assessments and Reviews", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-8", "component_code": "SR-8", "component_name": "Notification Agreements", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-10", "component_code": "SR-10", "component_name": "Inspection of Systems or Components", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-11", "component_code": "SR-11", "component_name": "Component Authenticity", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-11(1)", "component_code": "SR-11(1)", "component_name": "Component Authenticity | Anti-counterfeit Training", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-11(2)", "component_code": "SR-11(2)", "component_name": "Component Authenticity | Configuration Control for Component Service and Repair", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}, {"component_id": "sr-12", "component_code": "SR-12", "component_name": "Component Disposal", "component_type": "Control", "component_level": 2, "component_type_code": "control", "component_weightage": 2, "component_description": ""}]}], "ui_config": {}, "is_active": true, "extensions": {}}