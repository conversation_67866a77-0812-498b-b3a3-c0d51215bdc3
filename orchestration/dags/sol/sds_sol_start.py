"""
This is the HEAD DAG for Solution pipeline
"""
from datetime import datetime

from airflow.operators.dummy import DummyOperator
from commons.pe.sds_dag_orchestrator import SDSHeadDAG
from commons.sol.constants import SolConstants

with SDSHeadDAG(
        dag_id="sds_sol_start",
        description="DAG to start pipeline",
        pipeline_id=SolConstants.MODULE_NAME,
        start_date=datetime(2000, 1, 1),
        schedule_interval="00 12 * * *",
        render_template_as_native_obj=True,
        catchup=False,
        concurrency=1,
        tags=[SolConstants.MODULE_NAME, "DATA_ANALYTICS"],
) as ei_start_dag:
    start_sol_pipeline = DummyOperator(task_id="start_pipeline")