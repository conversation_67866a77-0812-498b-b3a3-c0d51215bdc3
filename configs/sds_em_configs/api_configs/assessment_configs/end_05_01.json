{"id": "END-05-01", "title": "Devices have host firewall protection enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment verifies host firewalls are enabled and properly configured on all devices. Disabled firewalls expose devices to network-based attacks and lateral movement.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host and Origin contains CrowdStrike", "Validate that 'Crowdstrike Firewall Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,origin as affected_asset_origin,crowdstrike_firewall_status from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_firewall_status=true", "finding_primary_key": "p_id", "finding_title": "Host firewall disabled", "finding_config": [{"title": "Crowdstrike Firewall Status", "expression": "crowdstrike_firewall_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-05"]}}