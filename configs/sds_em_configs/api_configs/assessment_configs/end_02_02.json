{"id": "END-02-02", "title": "Devices with EDR agents have sensor update policies enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 7, "is_active": true, "description": "This assessment checks whether sensor update policies are enabled on devices with EDR agents. These policies ensure timely updates to the sensor, maintaining effectiveness against evolving threats.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'Crowdstrike Sensor Update Policy Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_sensor_update_policy_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_sensor_update_policy_status=true", "finding_primary_key": "p_id", "finding_title": "Sensor update policy not enabled", "finding_config": [{"title": "Crowdstrike Sensor Update Policy Status", "expression": "crowdstrike_sensor_update_policy_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-02"]}}