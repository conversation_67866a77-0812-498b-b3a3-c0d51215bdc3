trigger_autoparser_dags:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON>
    retries: 1
    start_date: 2024-01-01
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: prevalentai/sds-platform-apps-qa-automation:<%PLATFORM_QA_IMAGE_VERSION%>
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      report_base_path: <%QA_REPORT_URI%>/behave
      run_prerequisites: false
      generate_report: true
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true      
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        TEST_ENV: "<%KUBE_NAMESPACE%>"
        EXEC_ENVIRONMENT: "<%EXEC_ENVIRONMENT%>"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        ICEBERG_WAREHOUSE: "<%DATALAKE_URI%>/iceberg/"
        ICEBERG_CATALOG_TYPE: "<%ICEBERG_CATALOG_TYPE%>"
        ICEBERG_CATALOG_URI: "<%ICEBERG_CATALOG_URI%>"
        HADOOP_FS_IMPL: "<%HADOOP_FS_IMPL%>"
        HADOOP_FS_END_POINT: "<%HADOOP_FS_ENDPOINT%>"
        SERVICE_ACCOUNT: "<%SERVICE_ACCOUNT%>"
        IMAGE: "prevalentai/sds-platform-apps-qa-automation:<%PLATFORM_QA_IMAGE_VERSION%>"
        EXECUTOR_MEMORY: "2g"
        DRIVER_MEMORY: "2g"
        EXECUTOR_INSTANCES: "2"
        EXECUTOR_CORES: "1"
        APP_NAME: "graphqlapi_sanity"
        UPDATED_AT: "{{data_interval_end.int_timestamp}}"
        DOMAIN_URL: "{{macros.custom_macros.get_env('DOMAIN_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        DATALAKE_URI: "<%DATALAKE_URI%>"
        PYICEBERG_CATALOG__DEFAULT__URI: "{{macros.custom_macros.get_env('PYICEBERG_CATALOG__DEFAULT__URI')}}"
        KEYCLOAK_SSL_VERIFY: "{{macros.custom_macros.get_env('KEYCLOAK_SSL_VERIFY', True)}}"
        DATALAKE_LOG_URI: "<%LOG_URI%>"
        AIRFLOW_API_ENDPOINT: http://airflow-webserver.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/orchestration
        INTERNAL_URL: http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080
        KGQL_URL: http://sds-platform-apps-graphql.<%KUBE_NAMESPACE%>.svc.cluster.local:8000/graphql
        KG_DISABLE_CACHE: "{{macros.custom_macros.get_env('KG_DISABLE_CACHE', True)}}"
        TENANT_ID: <%msitenant%>
        AZURE_MANAGED_IDENTITY_ID: <%clientid%> 
        REQUESTS_CA_BUNDLE: /mnt/CABundle/pythoncacerts
      volume_mounts:
      - mount_path: /mnt/CABundle
        sub_path: null
        name: pythoncacerts
        read_only: true
      volumes:
      - name: pythoncacerts
        configs:
          secret:
            defaultMode: 511
            secretName: pythoncacerts
      secrets:
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: AIRFLOW_API_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiUsername
      - deploy_type: env
        deploy_target: AIRFLOW_API_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: airflowApiPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
  tasks:
    start_task:
      operator: airflow.operators.empty.EmptyOperator
    run_different_autoparser_dags:
      startup_timeout_seconds: 300
      dependencies:
      - start_task
      feature_files:
      - src/platform/features/parsers_test.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - orchestration
      trigger_rule: "all_success"
    get_count_of_autoparser_dags:
      startup_timeout_seconds: 300
      dependencies:
      - run_different_autoparser_dags
      feature_files:
      - src/platform/features/parsers_test.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - srdm-count
      trigger_rule: "all_success"
    end_task:
      dependencies:
      - get_count_of_autoparser_dags
      operator: airflow.operators.empty.EmptyOperator
