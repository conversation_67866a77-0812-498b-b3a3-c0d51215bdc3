{"caption": "Host", "description": "An independent compute instance where we have visibility or management of the operating system.", "attributes": {"itop_pc_display_name": {"caption": "ServiceNow PC Display Name", "description": "Name of the host in ServiceNow PC.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_move_to_production_date": {"caption": "ServiceNow PC Move To Production Date", "description": "Move to production date of PC.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "defender_health_status": {"ui_visibility": false}, "defender_exposure_level": {"ui_visibility": false}, "av_status": {"ui_visibility": false}, "av_block_malicious_code_status": {"ui_visibility": false}, "defender_threat_name": {"ui_visibility": false}, "defender_action_type": {"ui_visibility": false}, "defender_id": {"ui_visibility": false}, "defender_detection_method": {"ui_visibility": false}, "win_event_id": {"ui_visibility": false}, "mdm_enrolled_date": {"ui_visibility": false}, "ad_last_sync_date": {"ui_visibility": false}, "av_last_scan_date": {"ui_visibility": false}, "av_signature_update_date": {"ui_visibility": false}, "ad_account_disabled_date": {"ui_visibility": false}, "defender_onboarding_date": {"ui_visibility": false}, "tenable_io_last_authenticated_scan_date": {"ui_visibility": false}, "tenable_io_last_scan_date": {"ui_visibility": false}, "tenable_io_asset_updated_at": {"ui_visibility": false}, "tenable_io_asset_aws_terminated_date": {"ui_visibility": false}, "tenable_io_onboarding_date": {"ui_visibility": false}, "defender_threat_count": {"ui_visibility": false}, "tenable_io_ipv4_addresses": {"ui_visibility": false}, "tenable_io_ipv6_addresses": {"ui_visibility": false}, "tenablesc_repositories": {"ui_visibility": false}, "tenablesc_asset_groups": {"ui_visibility": false}, "aws_instance_monitoring_state": {"caption": "AWS Instance Monitoring State", "description": "Checks whether detailed monitoring is enabled or disabled for a particular EC2 instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_instance_has_iam_role": {"caption": "AWS Instance IAM Role Assigned", "description": "Indicates whether the EC2 instance has an IAM role attached via an instance profile", "group": "source_specific", "type": "boolean", "ui_visibility": true}, "aws_instance_launch_date": {"caption": "AWS Instance Launch Date", "description": "Timestamp indicating when a specific EC2 instance was started or launched.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "environment": {"caption": "Environment", "description": "Tag that defines the environment in which cloud resources are deployed and used.Values typically include dev,staging,prod etc.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Resource Location and Availability"}, "aws_instance_vpc_id": {"caption": "AWS Instance VPC ID", "description": "Refers to the unique identifier assigned to the Virtual Private Cloud that the EC2 instance is part of.", "group": "source_specific", "type": "string", "ui_visibility": true}, "itop_pc_end_of_warranty_date": {"caption": "ServiceNow PC End of Warranty Date", "description": "End of Warranty date of PC.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_status": {"caption": "ServiceNow PC Status", "description": "Host status as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_org_id": {"caption": "ServiceNow PC Organization ID", "description": "Unique ID for the organization name.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_org_unit": {"caption": "ServiceNow PC Organization Unit", "description": "Organization unit of host as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_business_criticality": {"caption": "ServiceNow PC Business Criticality", "description": "Business criticality of a PC as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_device_serial_number": {"caption": "ServiceNow PC Device Serial Number", "description": "Device serial number of PC as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_location": {"caption": "ServiceNow PC Location", "description": "Location of PC as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_obsolete_status": {"caption": "ServiceNow PC Obsolete Status", "description": "Checks whether the PC has been obsoleted or not.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_obsolete_date": {"caption": "ServiceNow PC Obsolete Date", "description": "Date at with PC was obsoleted.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_brand": {"caption": "ServiceNow PC Brand", "description": "<PERSON> of the PC.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_model": {"caption": "ServiceNow PC Model", "description": "Model of the PC.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_cpu": {"caption": "ServiceNow PC CPU Name", "description": "CPU used in the device.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_ram": {"caption": "ServiceNow PC RAM Name", "description": "RAM used in the device.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_type": {"caption": "ServiceNow PC Device Type", "description": "Type of Device.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_os_family": {"caption": "ServiceNow PC OS Family", "description": "Classifies the OS into its corresponding platforms such as Windows, Linux, macOS, Android, iOS.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_os_version": {"caption": "ServiceNow PC OS Version", "description": "Version of the OS.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_os_build": {"caption": "ServiceNow PC OS Build", "description": "Operating System Build.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_asset_number": {"caption": "ServiceNow PC Asset Number", "description": "Asset number of the PC assigned by ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_pc_purchase_date": {"caption": "ServiceNow PC Purchase Date", "description": "Purchase date of the device.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_class": {"caption": "ServiceNow Class", "description": "Class name of the asset as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vmware_name": {"caption": "ServiceNow VMware Name", "description": "Virtual machine name as per ServiceNow VM.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_hypervisor_obsolete_status": {"caption": "ServiceNow VM Hypervisor Obsolete Status", "description": "Check whether the hypervisor is obsolete or not.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_hypervisor_obsolete_date": {"caption": "ServiceNow VM Hypervisor Obsolete Date", "description": "Check whether the hypervisor is obsolete or not.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_organization": {"caption": "ServiceNow VM organization", "description": "Organization name as per ServiceNow VM.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_hypervisor_full_name": {"caption": "ServiceNow VM Hypervisor Full Name", "description": "Hypervisor full name as seen in ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_hypervisor_family_name": {"caption": "ServiceNow VM Hypervisor Family Name", "description": "Hypervisor family name as seen in ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_server_obsolete_status": {"caption": "ServiceNow VM Server Obsolete Status", "description": "Check whether the vm server is obsolete or not.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_farm_name": {"caption": "ServiceNow VM Farm Name", "description": "Collection of virtual machines (VMs) that are managed together.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_business_criticity": {"caption": "ServiceNow VM Business Criticality", "description": "Business criticality of a hypervisor as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_vm_move_to_production_date": {"caption": "ServiceNow VM Move To Production Date", "description": "Move to production date of VM.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_display_name": {"caption": "ServiceNow Server Display Name", "description": "Indicates name of the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_organization": {"caption": "ServiceNow Server Organization", "description": "Organization name as per ServiceNow Server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_org_id": {"caption": "ServiceNow Server Organization ID", "description": "Unique ID for the organization name.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_status": {"caption": "ServiceNow Server Status", "description": "Server status as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_business_criticality": {"caption": "ServiceNow Server Business Criticality", "description": "Business criticality of a server as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_location": {"caption": "ServiceNow Server Location", "description": "Location of server as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_rack_name": {"caption": "ServiceNow Server Rack Name", "description": "Name of the physical rack where the server devices kept in.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_enclosure_name": {"caption": "ServiceNow Server Enclosure Name", "description": "Name of the physical enclosure or cage where the server devices kept in.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_obsolete_status": {"caption": "ServiceNow Server Obsolete Status", "description": "Name of the physical enclosure or cage where the server devices kept in.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_obsolete_date": {"caption": "ServiceNow Server Obsolete Date", "description": "Date at with Server was obsoleted.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_brand": {"caption": "ServiceNow Server Brand Name", "description": "Brand of the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_model": {"caption": "ServiceNow Server Model Name", "description": "Model of the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_os_family": {"caption": "ServiceNow Server OS Family", "description": "OS Family of the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_os_version": {"caption": "ServiceNow Server OS Version", "description": "OS version of the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_management_ip": {"caption": "ServiceNow Server IP Address", "description": "Internal IP address of the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_os_license": {"caption": "ServiceNow Server OS License", "description": "OS License of the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_cpu": {"caption": "ServiceNow Server CPU Name", "description": "CPU used in the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_ram": {"caption": "ServiceNow Server RAM Name", "description": "RAM used in the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_serial_number": {"caption": "ServiceNow Server Serial Number", "description": "Device serial number of server as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_asset_number": {"caption": "ServiceNow Server Asset Number", "description": "Asset number of server as per ServiceNow.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_move_to_production_date": {"caption": "ServiceNow Server Move To Production Date", "description": "Move to production date of server.", "group": "source_specific", "type": "timestamp", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_purchase_date": {"caption": "ServiceNow Server Purchase Date", "description": "Purchase date of server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_end_of_warranty_date": {"caption": "ServiceNow Server End of Warranty Date", "description": "End of warranty date of server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_primary_power_source": {"caption": "ServiceNow Server Primary Power Source", "description": "Primary power source used by the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "itop_server_secondary_power_source": {"caption": "ServiceNow Server Secondary Power Source", "description": "Secondary power source used by the server.", "group": "source_specific", "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "location": {"caption": "Location", "description": "Location of the entity", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "dashboard_identifier": {"EI": {}, "Host": {}}}, "project": {"caption": "Project", "description": "Field enriched from solution", "examples": "test", "group": "enrichment", "ui_visibility": true, "type": "string", "source": "test", "derived_field": false, "candidate_key": false, "dashboard_identifier": {"VRA": {"is_active": false}, "EI": {}, "Host": {}}}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "description": "Number of vulnerability findings associated with host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "step_interval": 40, "dashboard_identifier": {"EI": {}, "Host": {}}}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 30 days.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "type": "string", "derived_field": true, "enable_hiding": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "login_last_user": {"caption": "Last Logged in User", "description": "Last user logged in to the host.", "examples": "james", "group": "entity_specific", "type": "string", "source": ["Windows Security Logs"], "ui_visibility": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "ad_sam_account_name": {"caption": "AD SAM Account Name", "description": "SAM account name belonging to the machine account associated with this host.", "examples": "SOF-GABBGEO-MOY$", "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "ui_visibility": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "health_status": {"caption": "EDR Health Status", "description": "The device status assigned by the Microsoft Defender to the devices scanned.", "examples": ["Active", "NoSensorData", "ImpairedCommunication", "Inactive"], "group": "source_specific", "ui_visibility": true, "type": "string", "source": ["MS Defender"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "fw_status": {"caption": "Firewall Enabled", "description": "Indicates whether the firewall feature or service is enabled (true) or disabled (false) on the system. This field is a boolean value, with 'true' signifying that the firewall is operational and 'false' indicating it is inactive. Example values include 'true' and 'false'.\n", "group": "entity_specific", "type": "string", "ui_visibility": false, "category": "Security and Compliance", "navigator_attribute_description": "Firewall status indicates whether the firewall service is enabled (true) or disabled (false), and is a boolean value sourced solely from Defender.", "navigator_attribute_distinct_values": [false, true], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "risk_score": {"caption": "EDR Risk Score", "description": "Risk score as evaluated by Microsoft Defender for Endpoint", "examples": ["Low", "Medium", "Informational"], "group": "source_specific", "ui_visibility": true, "type": "string", "source": ["MS Defender"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "total_active_vulnerabilities": {"caption": "Total Active Vulnerabilities in the device", "description": "Total number of active vulnerabilities.", "examples": "11", "group": "source_specific", "ui_visibility": true, "type": "integer", "source": ["Qualys"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "total_new_vulnerabilities": {"caption": "Total New Vulnerabilities", "description": "Total number of new vulnerabilities.", "examples": "12", "group": "source_specific", "ui_visibility": true, "type": "integer", "source": ["Qualys"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "total_fixed_vulnerabilities": {"caption": "Total Vulnerabilities Fixed", "description": "Total number of fixed vulnerabilities.", "examples": "13", "group": "source_specific", "ui_visibility": true, "type": "integer", "source": ["Qualys"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "total_vulnerabilities": {"caption": "Total Vulnerabilities", "description": "Total number of vulnerabilities.", "examples": "14", "group": "source_specific", "ui_visibility": true, "type": "integer", "source": ["Qualys"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "ad_vpn_status": {"caption": "AD VPN Status", "description": "The status of the VPN in the AD.", "examples": ["Active", "Disabled"], "group": "source_specific", "ui_visibility": true, "type": "string", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_fim_policy_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike FIM Status", "ui_visibility": true, "examples": "True", "description": "The status of file integrity monitoring in crowdstrike : true or false", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_firewall_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Firewall Status", "examples": "True", "ui_visibility": true, "description": "The status of firewall in crowdstrike : true or false", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_sensor_uninstall_protection_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Sensor Uninstall Protection Status", "examples": "true,false", "ui_visibility": true, "description": "Sensor Uninstall Protection", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_prevention_policy_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Prevention Policy Status", "examples": "true", "ui_visibility": true, "description": "Prevention policy status", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_sensor_update_policy_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Sensor Update Policy Status", "examples": "true", "ui_visibility": true, "description": "Sensor update policy status", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_remote_response_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Remote Response Status", "examples": "true", "ui_visibility": true, "description": "CrowdStrike Remote Response Status field indicates whether the Real Time Response (RTR) feature is enabled and applied on the endpoint", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_host_retention_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Host Retention Status", "examples": "true", "ui_visibility": true, "description": "CrowdStrike Host Retention Status field indicates whether the Host Data Retention policy is applied to the endpoint. This policy controls how long data is retained on the host sensor", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_global_config_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Global Config Status", "examples": "true", "ui_visibility": true, "description": "The CrowdStrike Global Config Status field indicates whether the Global Configuration policy is applied to a device", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_content_update_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Content Update Status", "examples": "true", "ui_visibility": true, "description": "The CrowdStrike Content Update Status field indicates whether the Content Update Policy is applied to the device. This policy governs the automatic delivery of updated detection content to the Falcon sensor.", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_device_control_status": {"type": "boolean", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Device Control Status", "examples": "true", "ui_visibility": true, "description": "The CrowdStrike Device Control Status field indicates whether the Device Control policy is applied to the endpoint. This policy governs the usage and control of peripheral devices.", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_last_report_sla_duration": {"type": "bigint", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Last Report SLA Duration", "examples": "true", "description": "Represents the number of days since the asset last reported to CrowdStrike", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "crowdstrike_last_report_sla_status": {"type": "string", "group": "source_specific", "source": ["Crowdstrike"], "caption": "Crowdstrike Last Report SLA Duration", "examples": "true", "description": "Represents whether the asset last reported to CrowdStrike within the last 7 days", "derived_field": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "aad_compliance_status": {"type": "string", "group": "source_specific", "caption": "AAD Compliance Status", "examples": "true", "ui_visibility": true, "description": "Compliance status of the entity in AAD", "dashboard_identifier": {"EI": {}, "Host": {}}}, "aad_management_status": {"type": "string", "group": "source_specific", "caption": "AAD Management Status", "examples": "true", "ui_visibility": true, "description": "Management status of the entity in AAD", "dashboard_identifier": {"EI": {}, "Host": {}}}, "ad_vpn_disabled_date": {"caption": "AD VPN Disabled Date", "description": "The VPN disabled date of the host in the AD.", "examples": "1677069469000", "group": "source_specific", "ui_visibility": true, "type": "timestamp", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "active_threat_count": {"caption": "Active Threat Count", "description": "The current active threat count.", "examples": "15", "group": "source_specific", "ui_visibility": true, "type": "integer", "source": ["MS Defender"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "private_dns_name": {"caption": "Private DNS Name", "description": "It refers to a domain name system (DNS) name that is not publicly available on the Internet, but instead is used within a private network such as a corporate network or a home network.", "examples": "ip-10-86-195-72.eu-west-1.compute.internal", "group": "source_specific", "ui_visibility": true, "type": "string", "source": ["AWS Resource"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"VRA": {"is_active": false}, "EI": {}, "Host": {}}}, "data_source_dev": {"caption": "Source of the Data", "description": "Specifies the origin whether the data is based on EDM or SIT.", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "dashboard_identifier": {"VRA": {"label": "Source of Asset Data", "enable_hiding": true}, "EI": {}, "Host": {}}}, "aws_eks_cluster_name": {"dashboard_identifier": {"VRA": {"is_active": false}, "EI": {}, "Host": {}}}, "business_unit": {"dashboard_identifier": {"CCM": {"data_structure": "list", "ccm_org_group_by_enabled": true, "ccm_org_default": false}, "EI": {}, "Host": {}}}, "location_country": {"dashboard_identifier": {"CCM": {"data_structure": "list", "ccm_org_group_by_enabled": true, "ccm_org_default": true}, "EI": {}, "Host": {}}}}}