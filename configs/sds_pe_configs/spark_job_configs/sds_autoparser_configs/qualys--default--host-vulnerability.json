{"customJsonSchema": "{ \"type\": \"struct\", \"fields\": [ { \"name\": \"DETECTION_LIST\", \"type\": { \"type\": \"struct\", \"fields\": [ { \"name\": \"DETECTION\", \"type\": { \"type\": \"array\", \"elementType\": { \"type\": \"struct\", \"fields\": [ { \"name\": \"FIRST_FOUND_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"IS_DISABLED\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"IS_IGNORED\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_FIXED_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_FOUND_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_PROCESSED_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_TEST_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_UPDATE_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"FIRST_REOPENED_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_REOPENED_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"TIMES_REOPENED\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"PORT\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"PROTOCOL\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"QID\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"RESULTS\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"SEVERITY\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"SSL\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"STATUS\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"TIMES_FOUND\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"TYPE\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"UNIQUE_VULN_ID\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} } ] }, \"containsNull\": true }, \"nullable\": true, \"metadata\": {} } ] }, \"nullable\": true, \"metadata\": {} }, { \"name\": \"DNS\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"DNS_DATA\", \"type\": { \"type\": \"struct\", \"fields\": [ { \"name\": \"DOMAIN\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"FQDN\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"HOSTNAME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} } ] }, \"nullable\": true, \"metadata\": {} }, { \"name\": \"IP\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_SCAN_DATETIME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_VM_AUTH_SCANNED_DATE\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_VM_AUTH_SCANNED_DURATION\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_VM_SCANNED_DATE\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"LAST_VM_SCANNED_DURATION\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"NETBIOS\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"NETWORK_ID\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"OS\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"OS_CPE\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"QG_HOSTID\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"TRACKING_METHOD\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"event_timestamp\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"TAGS\", \"type\": { \"type\": \"struct\", \"fields\": [ { \"name\": \"TAG\", \"type\": { \"type\": \"array\", \"elementType\": { \"type\": \"struct\", \"fields\": [ { \"name\": \"NAME\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} }, { \"name\": \"TAG_ID\", \"type\": \"string\", \"nullable\": true, \"metadata\": {} } ] }, \"containsNull\": true }, \"nullable\": true, \"metadata\": {} } ] }, \"nullable\": true, \"metadata\": {} } ] }", "transformationConfig": [{"event_timestamp_epoch": "CAST(event_timestamp AS LONG)"}, {"detections": "explode_outer(detection_list.detection)"}, {"FIRST_FOUND_DATETIME": "detections.FIRST_FOUND_DATETIME"}, {"FIRST_REOPENED_DATETIME": "detections.FIRST_REOPENED_DATETIME"}, {"IS_DISABLED": "detections.IS_DISABLED"}, {"IS_IGNORED": "detections.IS_IGNORED"}, {"LAST_FIXED_DATETIME": "detections.LAST_FIXED_DATETIME"}, {"LAST_FOUND_DATETIME": "detections.LAST_FOUND_DATETIME"}, {"LAST_PROCESSED_DATETIME": "detections.LAST_PROCESSED_DATETIME"}, {"LAST_REOPENED_DATETIME": "detections.LAST_REOPENED_DATETIME"}, {"LAST_TEST_DATETIME": "detections.LAST_TEST_DATETIME"}, {"LAST_UPDATE_DATETIME": "detections.LAST_UPDATE_DATETIME"}, {"PORT": "detections.PORT"}, {"PROTOCOL": "detections.PROTOCOL"}, {"QID": "detections.QID"}, {"RESULTS": "detections.RESULTS"}, {"SEVERITY": "detections.SEVERITY"}, {"SSL": "detections.SSL"}, {"STATUS": "detections.STATUS"}, {"TIMES_FOUND": "detections.TIMES_FOUND"}, {"TYPE": "detections.TYPE"}, {"UNIQUE_VULN_ID": "detections.UNIQUE_VULN_ID"}, {"HOST_ID": "ID"}]}