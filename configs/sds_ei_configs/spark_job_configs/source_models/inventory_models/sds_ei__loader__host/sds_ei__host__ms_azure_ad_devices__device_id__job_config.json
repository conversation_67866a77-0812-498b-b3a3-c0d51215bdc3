{"temporaryProperties": [{"colName": "event_timestamp_epoch", "colExpr": "GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(registrationDateTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(onPremisesLastSyncDateTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(approximateLastSignInDateTime))),event_timestamp_epoch)"}, {"colName": "event_timestamp_ts", "colExpr": "to_timestamp(event_timestamp_epoch/1000)"}], "sourceSpecificProperties": [{"colExpr": "isCompliant", "colName": "aad_compliance_status"}, {"colExpr": "isManaged", "colName": "aad_management_status"}], "dataSource": {"name": "MS Azure AD", "feedName": "Devices", "srdm": "<%SRDM_SCHEMA_NAME%>.ms_azure_ad__devices"}}