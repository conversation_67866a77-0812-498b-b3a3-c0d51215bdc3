{"id": "END-01-03", "title": "Devices with EDR agents have sensor uninstall protection enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 7, "is_active": true, "description": "This assessment verifies whether sensor uninstall protection is enabled on devices with EDR agents. Enabling uninstall protection prevents unauthorized tampering or removal of EDR agents, ensuring continuous monitoring and endpoint defense.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'Crowdstrike Sensor Uninstall Protection Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_sensor_uninstall_protection_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_sensor_uninstall_protection_status=true", "finding_primary_key": "p_id", "finding_title": "Sensor uninstall protection not enabled", "finding_config": [{"title": "Crowdstrike Sensor Uninstall Protection Status", "expression": "crowdstrike_sensor_uninstall_protection_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-01"]}}