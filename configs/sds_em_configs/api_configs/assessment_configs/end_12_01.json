{"id": "END-12-01", "title": "Devices with EDR agents have device control enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 6, "is_active": true, "description": "This assessment verifies whether Device Control is enabled on endpoints that have been onboarded to the EDR agent. Device Control helps restrict or monitor the usage of peripheral devices such as USBs, preventing unauthorized data transfers and potential malware infections. Disabling this control could expose the organization to data exfiltration and endpoint compromise.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'Crowdstrike Device Control Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_device_control_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_device_control_status=true", "finding_primary_key": "p_id", "finding_title": "Crowdstrike Device Control Not Enabled", "finding_config": [{"title": "Device Control", "expression": "crowdstrike_device_control_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-12"]}}