{{- if index .Values "sds-client-ibx-jobs" "enabled" }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  annotations:
    notifications.argoproj.io/subscribe.on-deleted.teams: channelName
    notifications.argoproj.io/subscribe.on-health-degraded.teams: channelName
    notifications.argoproj.io/subscribe.on-sync-failed.teams: channelName
    notifications.argoproj.io/subscribe.on-sync-status-unknown.teams: channelName
    notifications.argoproj.io/subscribe.on-deleted.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
    notifications.argoproj.io/subscribe.on-health-degraded.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
    notifications.argoproj.io/subscribe.on-sync-failed.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
    notifications.argoproj.io/subscribe.on-sync-status-unknown.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
  name: {{ default (printf "%s-%s-%s" (index .Values "sds-client-ibx-jobs" "name") $.Values.global.PROJECT_NAME $.Values.global.ENVIRONMENT) .nameOverride}}
  namespace: argocd
spec:
  project: {{ default (printf "sds-%s-%s" $.Values.global.PROJECT_NAME $.Values.global.ENVIRONMENT) $.Values.projectOverride }}
  destination:
    server: {{ $.Values.global.CLUSTER_ENDPOINT }}
    namespace: {{ $.Values.global.DEPLOY_NAMESPACE }}
  sources:
    - repoURL: {{ $.Values.global.HELM_REPO }}
      path: sds-client-ibx/
      targetRevision: {{ include "sdsClientIte.targetRevision" . }}
      helm:
        valueFiles:
          - $values/{{ $.Values.global.COMMON_FILES_LOCATION }}
          - {{ default (printf "$values1/%s/sds-client-ibx/values.yaml" $.Values.global.OVERRIDE_FILES_LOCATION ) (.Values.overRideFilePath) }}
      chart: sds-client-ibx-jobs
    - repoURL: {{ $.Values.global.GIT_REPOSITORY_URL }}
      targetRevision: {{ default $.Values.global.GIT_BRANCHNAME .gitBranch }}
      ref: values
    - repoURL: {{ $.Values.global.GIT_APP_REPOSITORY_URL }}
      targetRevision: {{ default $.Values.global.GIT_APP_BRANCHNAME .gitBranch }}
      ref: values1
{{- end }}
