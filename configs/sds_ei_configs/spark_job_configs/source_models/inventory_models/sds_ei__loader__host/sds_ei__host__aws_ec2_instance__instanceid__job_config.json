{"dataSource": {"srdm": "<%SRDM_SCHEMA_NAME%>.aws__default__describe_instances"}, "temporaryProperties": [{"colName": "event_timestamp_epoch", "colExpr": "GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(LaunchTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(array_max(NetworkInterfaces.Attachment.AttachTime)))),UNIX_MILLIS(TIMESTAMP(to_timestamp(UsageOperationUpdateTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(REGEXP_EXTRACT(StateTransitionReason, '([0-9]{4}-[0-9]{2}-[0-9]{2})')))),ingested_timestamp_epoch)"}, {"colName": "event_timestamp_ts", "colExpr": "to_timestamp(event_timestamp_epoch/1000)"}], "entitySpecificProperties": [{"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(tags, x -> (CASE WHEN x.Key = 'env' THEN lower(x.Value) END)),ARRAY(NULL))[0]"}, {"colName": "instance_name", "colExpr": "lower(ARRAY_EXCEPT(TRANSFORM(tags , x -> (CASE WHEN x.Key = 'Name' THEN x.Value END)), array(NULL))[0])"}], "sourceSpecificProperties": [{"colName": "aws_instance_monitoring_state", "colExpr": "INITCAP(Monitoring.State)"}, {"colName": "aws_instance_launch_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LaunchTime)))"}, {"colName": "aws_instance_vpc_id", "colExpr": "VpcId"}]}