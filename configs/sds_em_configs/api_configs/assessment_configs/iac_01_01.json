{"id": "IAC-01-01", "title": "EC2 instances that are not leveraging IAM roles", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 7, "is_active": true, "description": "This assessment verifies that active AWS EC2 instances are configured with IAM roles. Lack of IAM roles may result in insecure access practices and increased identity-related risks.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Server and Workstation from Host where Origin contains AWS", "Validate that 'AWS EC2 Instance has IAM Role' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,aws_instance_has_iam_role,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server') and activity_status='Active' and ARRAY_CONTAINS(origin,'AWS')", "success_condition": "aws_instance_has_iam_role=true", "finding_primary_key": "p_id", "finding_title": "EC2 Instance Without IAM Role", "finding_config": [{"title": "AWS EC2 Instance has IAM Role", "expression": "aws_instance_has_iam_role", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["IAC-01"]}}