pre_requisite_pipeline:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: <PERSON><PERSON><PERSON> Kumar CR
    retries: 1
    start_date: 2024-01-01
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    create_airflow_connection_id:
      operator: airflow.operators.bash.BashOperator
      dependencies: [start_task]
      bash_command: >
        curl -X POST -k "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}/api/v1/connections" 
        -H "Content-Type: application/json" 
        -u "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}:{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}" 
        -d '{
          "connection_id": "airflow_conn",
          "conn_type": "http",
          "host": "{{macros.custom_macros.get_env('AIRFLOW_API_ENDPOINT')}}",
          "login": "{{macros.custom_macros.get_env('AIRFLOW_API_USERNAME')}}",
          "password": "{{macros.custom_macros.get_env('AIRFLOW_API_PASSWORD')}}"
        }'
    enable_data_migration_dag:
      operator: "airflow.providers.http.operators.http.SimpleHttpOperator"
      dependencies: [create_airflow_connection_id]
      http_conn_id: airflow_conn
      endpoint: '/api/v1/dags/sds_data_migration_dag'
      method: "PATCH"
      headers: 
        Content-Type: "application/json"
      data: '{"is_paused": false}'
      extra_options:
        verify: false
    trigger_data_migration_dag:
      operator: "airflow.operators.trigger_dagrun.TriggerDagRunOperator"
      dependencies: [enable_data_migration_dag]
      trigger_dag_id: "sds_data_migration_dag"
      wait_for_completion: true
    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [trigger_data_migration_dag]
