{"entity_attributes": {"asset_role": {"caption": "Asset Role", "description": "Describes the specific function or purpose of an asset within an organization.Contributed attributes are OS Family, Description, Type,Hostname, Asset Tag etc. Common roles include types such as 'Database', 'Production Server', and 'Domain Controller'.", "type": "string", "visualization_enabled": true}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "type": "string", "visualization_enabled": false}}}