{"id": "IAC-15-02", "title": "Devices in Azure AD registered in MDM are compliant with MDM policies", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 7, "is_active": true, "description": "This assessment ensures that Azure AD registered devices with MDM service enabled are compliant with MDM policies. Non-compliance may indicate misconfigured or unmanaged endpoints, increasing organizational risk.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active devices from Host where Origin contains MS Azure AD and AAD Management Service equals MDM", "Validate that 'AAD Compliance Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,aad_management_service,origin,origin as affected_asset_origin,aad_compliance_status from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where  activity_status='Active' and ARRAY_CONTAINS(origin,'MS Azure AD') and aad_management_service='MDM'", "success_condition": "aad_compliance_status=true", "finding_primary_key": "p_id", "finding_title": "MDM Non-Compliant Device", "finding_config": [{"title": "AAD Compliance Status", "expression": "aad_compliance_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["IAC-15"]}}