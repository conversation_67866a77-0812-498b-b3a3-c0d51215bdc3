{"data_migration_default_config": {"class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "driver_memory": "1g", "driver_cores": 1, "executor_cores": 1, "executor_memory": "4g", "executor_instances": 4, "spark_version": "3.5.1", "conf": {"spark.sql.caseSensitive": "true", "spark.sql.files.maxPartitionBytes": "128MB", "spark.default.parallelism": "4"}}, "data_migration_custom_config": {"data_interval_end": "2024-01-01", "microsoft_azure__defender_device_software_vuln_delta": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__defender_device_software_vuln_delta", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__defender_device_software_vuln_delta", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "infoblox_onprem_ddi__host": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.infoblox_onprem_ddi__host", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/infoblox_onprem_ddi__host", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__resource_details": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__resource_details", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__resource_details", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__security_resources": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__security_resources", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__security_resources", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "infoblox_onprem_ddi__host_enrichment": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.infoblox_onprem_ddi__host_enrichment", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/infoblox_onprem_ddi__host_enrichment", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__security_assessment": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__security_assessment", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__security_assessment", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "ms_azure_ad__user_registration_details": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.ms_azure_ad__user_registration_details", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/ms_azure_ad__user_registration_details", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__iam_list_users": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__iam_list_users", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__iam_list_users", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__describe_config_rules": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__describe_config_rules", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__describe_config_rules", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__sh_get_enabled_standards": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__sh_get_enabled_standards", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__sh_get_enabled_standards", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__sh_describe_standards_controls": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__sh_describe_standards_controls", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__sh_describe_standards_controls", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__sh_list_security_control_definitions": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__sh_list_security_control_definitions", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__sh_list_security_control_definitions", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__sh_list_standards_control_associations": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__sh_list_standards_control_associations", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__sh_list_standards_control_associations", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__iam_security_center_permission_set_assignment": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__iam_security_center_permission_set_assignment", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__iam_security_center_permission_set_assignment", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "open_data__epss_data": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.open_data__epss_data", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/open_data__epss_data", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__ad_user": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__ad_user", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__ad_user", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__cloudtrail": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__cloudtrail", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__cloudtrail", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__ad_device": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__ad_device", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__ad_device", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__emr_clusters": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__emr_clusters", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__emr_clusters", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "open_data__nvd_vulnerability": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.open_data__nvd_vulnerability", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/open_data__nvd_vulnerability", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__emr_ec2_fleet": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__emr_ec2_fleet", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__emr_ec2_fleet", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__sh_findings": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__sh_findings", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__sh_findings", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "cisa__vulnrichment": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.cisa__vulnrichment", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/cisa__vulnrichment", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__defender_device_list": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__defender_device_list", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__defender_device_list", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "rbac_project_lookup": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.rbac_project_lookup", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/rbac_project_lookup", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__emr_ec2_instance": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__emr_ec2_instance", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__emr_ec2_instance", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__resource_details": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "executor_memory": "8g", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__resource_details", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__resource_details", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "azure__aci_containers": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.azure__aci_containers", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/azure__aci_containers", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__defender_device_events": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__defender_device_events", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__defender_device_events", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__ecs_task_containers": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__ecs_task_containers", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__ecs_task_containers", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "bamboohr__hr_report_pull": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.bamboohr__hr_report_pull", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/bamboohr__hr_report_pull", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__ad_registered_user": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__ad_registered_user", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__ad_registered_user", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__ec2_describe_instances": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__ec2_describe_instances", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__ec2_describe_instances", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__ecs_service_containers": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__ecs_service_containers", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__ecs_service_containers", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__file_share": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__file_share", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__file_share", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__defender_device_software_vuln": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__defender_device_software_vuln", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__defender_device_software_vuln", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__identity_store_list_users": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__identity_store_list_users", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__identity_store_list_users", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__queue_storage": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__queue_storage", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__queue_storage", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__resource_list": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__resource_list", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__resource_list", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__table_storage": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__table_storage", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__table_storage", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__identity_store_list_groups": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__identity_store_list_groups", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__identity_store_list_groups", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__organization_list_accounts": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__organization_list_accounts", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__organization_list_accounts", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__ad_user_sign_in": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__ad_user_sign_in", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__ad_user_sign_in", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__defender_software": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__defender_software", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__defender_software", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__list_subscription": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__list_subscription", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__list_subscription", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__security_assesment": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__security_assesment", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__security_assesment", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__compliance_controls": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__compliance_controls", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__compliance_controls", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__ad_directory_members": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__ad_directory_members", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__ad_directory_members", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__compliance_standards": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__compliance_standards", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__compliance_standards", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__compliance_assessments": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__compliance_assessments", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__compliance_assessments", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__blob_storage_container": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__blob_storage_container", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__blob_storage_container", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__list_aggregate_discovered_resources": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__list_aggregate_discovered_resources", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__list_aggregate_discovered_resources", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws__identity_store_list_group_memberships": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws__identity_store_list_group_memberships", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws__identity_store_list_group_memberships", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__virtual_machine": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__virtual_machine", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__virtual_machine", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft_azure__defender_tvm_secure_config": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft_azure__defender_tvm_secure_config", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft_azure__defender_tvm_secure_config", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "mega": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.mega", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/mega", "--filter_expression", "parsed_interval_timestamp_ts >= '2020-01-01'", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "microsoft__active_directory": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.microsoft__active_directory", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/microsoft__active_directory", "--filter_expression", "parsed_interval_timestamp_ts >= '2020-01-01' ", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "aws_eks_container": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.aws_eks_container", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/aws_eks_container", "--filter_expression", "parsed_interval_timestamp_ts >= '2020-01-01' ", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}, "azure_aks_container": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-utility-jobs-3.5_2.12.jar", "class_name": "ai.prevalent.parquettoiceberg.ParquetToIceberg", "args": ["--spark-service", "spark", "--iceberg-table", "srdm.azure_aks_container", "--base<PERSON><PERSON>", "<%ITE_UTILS_URI%>/ite/srdm_compacted/azure_aks_container", "--filter_expression", "parsed_interval_timestamp_ts >= '2020-01-01' ", "--config-path", "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-configs/data_migration_config/srdm_copy.json"]}}}