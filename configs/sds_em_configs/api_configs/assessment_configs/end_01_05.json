{"id": "END-01-05", "title": "Devices with EDR agents have global configuration policy applied", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 7, "is_active": true, "description": "This assessment checks whether a global configuration policy is applied on devices with EDR agents. Lack of this policy may lead to inconsistent security settings and increased risk exposure.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'Crowdstrike Global Config Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_global_config_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_global_config_status=true", "finding_primary_key": "p_id", "finding_title": "Global Configuration Policy Not Applied", "finding_config": [{"title": "Crowdstrike Global Config Status", "expression": "crowdstrike_global_config_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-01"]}}