df_kg_publish_sanity_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: sithara_mohan
    retries: 1
    start_date: 2024-01-01
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: prevalentai/sds-solution-ei-qa-automation:<%KG_QA_IMAGE_VERSION%>
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      run_prerequisites: false
      generate_report: true
      report_base_path: "<%QA_REPORT_URI%>/behave/"
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        TEST_ENV: "qa"
        DEBUG_MODE: "true"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        ICEBERG_WAREHOUSE: "<%DATALAKE_URI%>/iceberg/"
        ICEBERG_CATALOG_TYPE: "<%ICEBERG_CATALOG_TYPE%>"
        ICEBERG_CATALOG_URI: "<%ICEBERG_CATALOG_URI%>"
        HADOOP_FS_IMPL: "<%HADOOP_FS_IMPL%>"
        HADOOP_FS_END_POINT: "<%HADOOP_FS_ENDPOINT%>"
        SERVICE_ACCOUNT: "<%SERVICE_ACCOUNT%>"
        IMAGE: "prevalentai/sds-solution-ei-qa-automation:<%KG_QA_IMAGE_VERSION%>"
        SDM_SCHEMA_NAME: "<%SDM_SCHEMA_NAME%>"
        EI_SCHEMA_NAME: "<%EI_SCHEMA_NAME%>"
        SRDM_SCHEMA_NAME: "<%SRDM_SCHEMA_NAME%>"
        EXECUTOR_MEMORY: "6g"
        DRIVER_MEMORY: "4g"
        EXECUTOR_INSTANCES: "4"
        EXECUTOR_CORES: "1"
        APP_NAME: "kg-sanity-test"
        UPDATED_AT: "{{data_interval_end.int_timestamp}}"
        SELENIUM_SDS3_URL: "{{macros.custom_macros.get_env('SELENIUM_SDS3_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        CONFIG_ARTIFACTORY_URI: "<%CONFIG_ARTIFACTORY_URI%>"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        MANAGEMENT_API_UI_MAPPING_ENDPOINT: "{{macros.custom_macros.get_env('MANAGEMENT_API_UI_MAPPING_ENDPOINT')}}"
        DATALAKE_URI: "s3a://{{macros.custom_macros.get_env('S3_DATALAKE_BUCKET_NAME')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        KEYCLOAK_SSL_VERIFY : "{{macros.custom_macros.get_env('KEYCLOAK_SSL_VERIFY', False)}}"
      secrets:
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: GIT_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: gitPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
  tasks:
    start_task:
      operator: "airflow.operators.empty.EmptyOperator"
    publish_sanity_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      dependencies: [start_task]
      tags: ["publish"]
      feature_files: ['src/knowledge_graph/data_test/features/fragments.feature']
    graph_properties_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      dependencies: [start_task]
      tags: ["graph_properties"]
      feature_files: ['src/knowledge_graph/data_test/features/fragments.feature']
    end_task:
      operator: "airflow.operators.empty.EmptyOperator"
      dependencies: [publish_sanity_test,graph_properties_test]
      trigger_rule: "all_done"
