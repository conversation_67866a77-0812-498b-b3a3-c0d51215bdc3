df_studio_sanity_dag:
  timetable:
    callable: manual_only_timetable.ManualOnlyTimetable
    params:
      description: "Manual only time table"
      schedule_interval: None
  user_defined_macros:
    os: os
  default_args:
    owner: Athira_Damodaran
    retries: 1
    start_date: 2024-01-01
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: prevalentai/sds-solution-studio-qa-automation:<%STUDIO_QA_IMAGE_VERSION%>
      namespace: "<%KUBE_NAMESPACE%>"
      image_pull_secrets: "docker-secret"
      run_prerequisites: false
      generate_report: true
      report_base_path: "<%QA_REPORT_URI%>/behave/"
      rerun: false
      is_delete_operator_pod: true
      log_events_on_failure: true      
      annotations:
        sidecar.istio.io/inject: "false"
      pod_runtime_info_envs:
        - name: "POD_IP"
          field_path: "status.podIP"
      service_account_name: "spark"
      env_vars:
        TEST_ENV: "<%KUBE_NAMESPACE%>"
        DEBUG_MODE: "true"
        NAMESPACE: "<%KUBE_NAMESPACE%>"
        DOMAIN_URL: "{{macros.custom_macros.get_env('DOMAIN_URL')}}"
        SELENIUM_SDS3_URL: "{{macros.custom_macros.get_env('SELENIUM_SDS3_URL')}}"
        KEYCLOAK_HOST: "{{macros.custom_macros.get_env('KEYCLOAK_HOST')}}"
        KEYCLOAK_REALM: "{{macros.custom_macros.get_env('KEYCLOAK_REALM')}}"
        AIRFLOW_CONN_ADMIN_API_BASE_URL: "{{macros.custom_macros.get_env('AIRFLOW_CONN_ADMIN_API_BASE_URL')}}"
        AIRFLOW_CONN_RCM_ADMIN_API: "{{macros.custom_macros.get_env('AIRFLOW_CONN_RCM_ADMIN_API')}}"
        SSL_VERIFY: "{{macros.custom_macros.get_env('SSL_VERIFY', False)}}"
        MANAGEMENT_API_UI_MAPPING_ENDPOINT: "{{macros.custom_macros.get_env('MANAGEMENT_API_UI_MAPPING_ENDPOINT')}}"
        DATALAKE_URI: "s3a://{{macros.custom_macros.get_env('S3_DATALAKE_BUCKET_NAME')}}"
        PYICEBERG_CATALOG__DEFAULT__URI: "{{macros.custom_macros.get_env('PYICEBERG_CATALOG__DEFAULT__URI')}}"
        KEYCLOAK_SSL_VERIFY: "{{macros.custom_macros.get_env('KEYCLOAK_SSL_VERIFY', True)}}"
        DATALAKE_LOG_URI: "<%LOG_URI%>"
        AIRFLOW_API_ENDPOINT: http://airflow-webserver.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/orchestration
        INTERNAL_URL: http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080
      secrets:
      - deploy_type: env
        deploy_target: SELENIUM_USERNAME
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumUsername
      - deploy_type: env
        deploy_target: SELENIUM_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: seleniumPassword
      - deploy_type: env
        deploy_target: KC_CLIENT_ID
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientId
      - deploy_type: env
        deploy_target: KC_CLIENT_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: clientSecret
      - deploy_type: env
        deploy_target: GIT_PASSWORD
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: gitPassword
      - deploy_type: env
        deploy_target: JIRA_SECRET
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: jiraSecret
      - deploy_type: env
        deploy_target: AWS_CONNECTOR_ACCESS_KEY
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: AWS_CONNECTOR_ACCESS_KEY
      - deploy_type: env
        deploy_target: AWS_CONNECTOR_SECRET_KEY
        secret: external-secret-vault-<%KUBE_NAMESPACE%>
        key: AWS_CONNECTOR_SECRET_KEY
  tasks:
    start_task:
      operator: airflow.operators.empty.EmptyOperator
    clear_epss_feeds:
      operator: airflow.operators.bash.BashOperator
      dependencies: [start_task]
      bash_command: >
        TOKEN=$(curl -X POST "{{macros.custom_macros.get_env('DOMAIN_URL')}}"/realms/"<%KUBE_NAMESPACE%>"/protocol/openid-connect/token 
        -d "grant_type=client_credentials&client_id={{macros.custom_macros.get_env('KC_CLIENT_ID', True)}}"&client_secret={{macros.custom_macros.get_env('KC_CLIENT_SECRET', True)}}""|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());print(data["access_token"]);'|awk '{print $1}') && 
        curl -X GET -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/ 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());data_feeds = list(map(lambda x: x["id"], filter(lambda x: x["connection_label"] == "EPSS", data["data"])));
        print(data_feeds)'|while read value; do response=$(curl -s -o response.txt -w "%{response_code}" -X DELETE -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/datafeeds/$value 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN");echo $response; done
    clear_epss_connections:
      operator: airflow.operators.bash.BashOperator
      dependencies: [start_task]
      bash_command: >
        TOKEN=$(curl -X POST "{{macros.custom_macros.get_env('DOMAIN_URL')}}"/realms/"<%KUBE_NAMESPACE%>"/protocol/openid-connect/token 
        -d "grant_type=client_credentials&client_id={{macros.custom_macros.get_env('KC_CLIENT_ID', True)}}"&client_secret={{macros.custom_macros.get_env('KC_CLIENT_SECRET', True)}}""|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());print(data["access_token"]);'|awk '{print $1}') && 
        curl -X GET -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/ 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());connection = list(filter(lambda x: x["connection_label"] == "EPSS", data["data"]));
        conn_id = connection[0]["id"] if connection else None;print(conn_id)'|
        xargs -I{} curl -X DELETE -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/{} 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"
    epss_ingestion_task:
      dependencies: [clear_epss_feeds, clear_epss_connections]
      feature_files:
      - src/studio_ui/features/studio_connectors.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - studio-sanity-test-epss
    clear_epss_advanced_feeds:
      operator: airflow.operators.bash.BashOperator
      dependencies: [epss_ingestion_task]
      bash_command: >
        TOKEN=$(curl -X POST "{{macros.custom_macros.get_env('DOMAIN_URL')}}"/realms/"<%KUBE_NAMESPACE%>"/protocol/openid-connect/token 
        -d "grant_type=client_credentials&client_id={{macros.custom_macros.get_env('KC_CLIENT_ID', True)}}"&client_secret={{macros.custom_macros.get_env('KC_CLIENT_SECRET', True)}}""|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());print(data["access_token"]);'|awk '{print $1}') && 
        curl -X GET -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/ 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());data_feeds = list(map(lambda x: x["id"], filter(lambda x: x["connection_label"] == "EPSS", data["data"])));
        print(data_feeds)'|while read value; do response=$(curl -s -o response.txt -w "%{response_code}" -X DELETE -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/datafeeds/$value 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN");echo $response; done
    clear_epss_advanced_connections:
      operator: airflow.operators.bash.BashOperator
      dependencies: [epss_ingestion_task]
      bash_command: >
        TOKEN=$(curl -X POST "{{macros.custom_macros.get_env('DOMAIN_URL')}}"/realms/"<%KUBE_NAMESPACE%>"/protocol/openid-connect/token 
        -d "grant_type=client_credentials&client_id={{macros.custom_macros.get_env('KC_CLIENT_ID', True)}}"&client_secret={{macros.custom_macros.get_env('KC_CLIENT_SECRET', True)}}""|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());print(data["access_token"]);'|awk '{print $1}') && 
        curl -X GET -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/ 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());connection = list(filter(lambda x: x["connection_label"] == "EPSS", data["data"]));
        conn_id = connection[0]["id"] if connection else None;print(conn_id)'|
        xargs -I{} curl -X DELETE -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/{} 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"
    epss_ingestion_advanced_task:
      dependencies: [clear_epss_advanced_connections, clear_epss_advanced_feeds]
      feature_files:
      - src/studio_ui/features/studio_connectors.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - studio-sanity-test-epss-advanced
    clear_aws_feeds:
      operator: airflow.operators.bash.BashOperator
      dependencies: [epss_ingestion_advanced_task]
      bash_command: >
        TOKEN=$(curl -X POST "{{macros.custom_macros.get_env('DOMAIN_URL')}}"/realms/"<%KUBE_NAMESPACE%>"/protocol/openid-connect/token 
        -d "grant_type=client_credentials&client_id={{macros.custom_macros.get_env('KC_CLIENT_ID', True)}}"&client_secret={{macros.custom_macros.get_env('KC_CLIENT_SECRET', True)}}""|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());print(data["access_token"]);'|awk '{print $1}') && 
        curl -X GET -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/ 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());data_feeds = list(map(lambda x: x["id"], filter(lambda x: x["connection_label"] == "AWS", data["data"])));
        print(data_feeds)'|while read value; do response=$(curl -s -o response.txt -w "%{response_code}" -X DELETE -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/datafeeds/$value 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN");echo $response; done
    clear_aws_connections:
      operator: airflow.operators.bash.BashOperator
      dependencies: [epss_ingestion_advanced_task]
      bash_command: >
        TOKEN=$(curl -X POST "{{macros.custom_macros.get_env('DOMAIN_URL')}}"/realms/"<%KUBE_NAMESPACE%>"/protocol/openid-connect/token 
        -d "grant_type=client_credentials&client_id={{macros.custom_macros.get_env('KC_CLIENT_ID', True)}}"&client_secret={{macros.custom_macros.get_env('KC_CLIENT_SECRET', True)}}""|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());print(data["access_token"]);'|awk '{print $1}') && 
        curl -X GET -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/ 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"|
        python -c 'import sys, json; data = json.loads(sys.stdin.read());connection = list(filter(lambda x: x["connection_label"] == "AWS", data["data"]));
        conn_id = connection[0]["id"] if connection else None;print(conn_id)'|
        xargs -I{} curl -X DELETE -k http://mgmtapis.<%KUBE_NAMESPACE%>.svc.cluster.local:8080/sds_mgmnt/ingestion/api/v1/connection/{} 
        -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN"
    aws_ingestion_task:
      dependencies: [clear_aws_connections, clear_aws_feeds]
      feature_files:
      - src/studio_ui/features/studio_connectors.feature
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags:
      - studio-sanity-test-aws
    end_task:
      dependencies:
      - aws_ingestion_task
      operator: airflow.operators.empty.EmptyOperator
      trigger_rule: "all_done"

  
