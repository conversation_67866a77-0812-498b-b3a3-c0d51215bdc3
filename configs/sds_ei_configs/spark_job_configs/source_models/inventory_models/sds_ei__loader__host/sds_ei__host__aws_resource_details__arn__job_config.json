{"dataSource": {"srdm": "<%SRDM_SCHEMA_NAME%>.aws__default__resource_details"}, "origin": "'AWS Resource Details'", "temporaryProperties": [{"colName": "event_timestamp_epoch", "colExpr": "GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.launchTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(array_max(configuration.NetworkInterfaces.Attachment.AttachTime)))),UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.lastModified))),ingested_timestamp_epoch)"}, {"colName": "event_timestamp_ts", "colExpr": "to_timestamp(event_timestamp_epoch/1000)"}], "entitySpecificProperties": [{"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'env' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "instance_name", "colExpr": "CASE WHEN native_type = 'AWS EC2 Instance' THEN ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Name' THEN x.value END)), array(NULL))[0] ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_instance_monitoring_state", "colExpr": "INITCAP(configuration.monitoring.state)"}, {"colName": "aws_instance_has_iam_role", "colExpr": "case when configuration.iamInstanceProfile.arn is not null then true else false end"}, {"colName": "aws_instance_launch_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.launchTime)))"}, {"colName": "aws_instance_vpc_id", "colExpr": "VpcId"}]}