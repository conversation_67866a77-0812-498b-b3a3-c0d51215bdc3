{"caption": "Vulnerability", "description": "The Vulnerability Dictionary defines attributes defined for Vulnerability entity derived from multiple sources.", "attributes": {"activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 30 days.\nFor Vulnerability entity it is based on number of open findings associated.Inactive if no open findings.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "enable_hiding": false, "group": "common", "type": "string", "dashboard_identifier": {"EI": {}, "Vulnerability": {}}}, "title": {"caption": "Title", "description": "Title of the vulnerability.", "examples": "Accellion FTA OS Command Injection Vulnerability", "group": "entity_specific", "ui_visibility": true, "type": "string", "source": ["Qualys"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Vulnerability": {}}}, "v3_severity": {"caption": "CVSSv3 Severity", "description": "The severity of the vulnerability.", "examples": ["Critical", "High", "Medium", "Low"], "group": "entity_specific", "ui_visibility": true, "type": "string", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Vulnerability": {}}}, "v3_score": {"caption": "CVSSv3 Score", "description": "Represent the severity of a vulnerability but not whether a vulnerability poses a risk to a specific IT environment.", "examples": "2.0", "group": "entity_specific", "ui_visibility": true, "type": "double", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Vulnerability": {}}}, "v3_vector": {"caption": "CVSSv3 Vector", "description": "A compressed textual representation of the values used to derive the base and temporal score.", "examples": "The attacker connects to the exploitable MySQL database over a network.", "group": "entity_specific", "ui_visibility": true, "type": "string", "source": ["Qualys"], "derived_field": false, "candidate_key": false, "dashboard_identifier": {"EI": {}, "Vulnerability": {}}}, "data_source_dev": {"caption": "Source of the Data", "description": "Specifies the origin whether the data is based on EDM or SIT.", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "dashboard_identifier": {"VRA": {"label": "Source of Vulnerability Data", "enable_hiding": true}, "EI": {}, "Vulnerability": {}}}, "business_unit": {"dashboard_identifier": {"CCM": {"data_structure": "list", "ccm_org_group_by_enabled": true, "ccm_org_default": false}, "EI": {}, "Vulnerability": {}}}, "location_country": {"dashboard_identifier": {"CCM": {"data_structure": "list", "ccm_org_group_by_enabled": true, "ccm_org_default": true}, "EI": {}, "Vulnerability": {}}}, "cve_id": {"dashboard_identifier": {"CCM": {"is_active": false}, "EI": {}, "Vulnerability": {}}}, "vulnerability_severity": {"dashboard_identifier": {"CCM": {"is_active": false}, "EI": {}, "Vulnerability": {}}}}}