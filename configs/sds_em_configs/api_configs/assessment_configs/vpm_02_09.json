{"id": "VPM-02-09", "title": "Software vulnerabilities on network devices are closed", "contributing_module": ["Exposure"], "assessment_weightage": 1, "is_active": true, "description": "This assessment verifies that software vulnerabilities on network devices are closed. Unresolved vulnerabilities on internal systems increase the risk of lateral movement and internal compromise by adversaries.", "scope_entity": ["Host", "Vulnerability"], "scope_validation_steps": ["Select Active Network Device from Host", "Use 'Host Has Vulnerability Finding' relationship from Graph filter", "Validate that 'Finding Status' is Closed"], "scope_query": "select reln.relationship_id as relationship_id, ARRAY(STRUCT(host.p_id, host.class, host.display_label,host.activity_status), STRUCT(vuln.p_id, vuln.class, vuln.display_label,vuln.activity_status)) AS affected_asset,reln.origin as affected_asset_origin,reln.current_status as current_status FROM <%EI_SCHEMA_NAME%>.sds_ei__host__enrich AS host INNER JOIN <%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host__enrich AS reln ON host.p_id = reln.target_p_id INNER JOIN <%EI_SCHEMA_NAME%>.sds_ei__vulnerability__enrich AS vuln ON reln.source_p_id = vuln.p_id where host.activity_status='Active' AND host.type='Network Device'", "success_condition": "current_status='Closed'", "finding_primary_key": "relationship_id", "finding_title": "<--affected_asset[1].display_label-->", "finding_config": [{"title": "Vulnerability Finding Status", "expression": "current_status", "finding_evidence": true}], "exposure_category": "Software Vulnerability", "control_mapping": {"SCF": ["VPM-02", "VPM-04", "VPM-05.3", "EMB-07"], "PCI DSS v4.0.1": []}}