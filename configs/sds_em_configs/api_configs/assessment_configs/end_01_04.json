{"id": "END-01-04", "title": "Devices with EDR agents have host retention policy applied", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 7, "is_active": true, "description": "This assessment verifies whether a host retention policy is applied to devices onboarded with EDR agents. Retention policies ensure that endpoint telemetry and logs are stored for a sufficient duration to support threat investigations, compliance requirements, and forensic analysis. Devices without an active retention policy may hinder incident response and data recovery efforts.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Hypervisor, Server, and Workstation from Host where EDR Onboarding Status is true and Origin contains CrowdStrike", "Validate that 'Crowdstrike Host Retention Policy Status' is true"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,crowdstrike_host_retention_status,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server' or type='Hypervisor') and activity_status='Active' and edr_onboarding_status=true and ARRAY_CONTAINS(origin, 'CrowdStrike')", "success_condition": "crowdstrike_host_retention_status=true", "finding_primary_key": "p_id", "finding_title": "Host Retention Policy Not Applied", "finding_config": [{"title": "Crowdstrike Host Retention Policy Status", "expression": "crowdstrike_host_retention_status", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["END-01"]}}