{"autoparser_default_job_configuration": {"class_name": "ai.prevalent.sdsautoparser.parserimpl.json.JSONParser", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-auto-parser-assembly.jar", "driver_memory": "6g", "driver_cores": 1, "executor_cores": 2, "executor_memory": "2g", "executor_instances": 2, "spark_version": "3.2.3", "args": ["--spark-service", "spark"]}, "autoparser_custom_job_configuration": {"xml-parsed-test": {"class_name": "ai.prevalent.sdsautoparser.parserimpl.xml.XMLParser"}, "microsoft--windows-security-logs": {"class_name": "ai.prevalent.sdsautoparser.parserimpl.regex.subpatternrouting.RegexSubPatternRoutingParser", "jars": ["<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-dbt-udf-spark_2.12.jar"], "driver_memory": "2g", "driver_cores": 1, "executor_instances": 3, "executor_memory": "9g", "executor_cores": 3}, "palo-alto--global-protect": {"driver_memory": "2g", "executor_cores": 2, "executor_instances": 3, "executor_memory": "5g", "conf": {"spark.sql.files.maxPartitionBytes": "*********"}, "class_name": "ai.prevalent.sdsautoparser.parserimpl.csv.CSVParser", "jars": ["<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-pe-dbt-udf-spark_2.12.jar"]}}}