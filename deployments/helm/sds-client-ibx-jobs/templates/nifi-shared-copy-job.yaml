{{- if .Values.jobOverrides.sds_nifi_shared_copy_job.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: nifi-shared-copy-job
  labels:
    job: {{ .Values.labelJob }}
    {{- include "k8jobs.labels" . | nindent 4 }}
spec:
  backoffLimit: {{ default .Values.backoffLimit .Values.jobOverrides.sds_nifi_shared_copy_job.backoffLimit }}
  activeDeadlineSeconds: {{ default .Values.activeDeadlineSeconds .Values.jobOverrides.sds_nifi_shared_copy_job.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ default .Values.ttlSecondsAfterFinished .Values.jobOverrides.sds_nifi_shared_copy_job.ttlSecondsAfterFinished }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "k8jobs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ (index .Values "jobOverrides" "sds_nifi_shared_copy_job" "image" "repository") }}:{{ (index .Values "jobOverrides" "sds_nifi_shared_copy_job" "image" "tag") | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with index .Values "jobOverrides" "sds_nifi_shared_copy_job" }}
          command: {{ tpl (toYaml .command) $ | nindent 12 }}
          {{- end }}           
              
          {{- if .Values.args }}
          args: {{ tpl (toYaml .Values.args) . | nindent 12 }}
          {{- end }}
          envFrom: []
       
          env:
            {{- include "custom_nifi_shared_copy_job_environment" . | indent 10 }}          
            - name: ARTIFACT_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: jfrogUsername
            - name: ARTIFACT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: jfrogPassword
          resources:
            {{- toYaml (index .Values "jobOverrides" "sds_nifi_shared_copy_job" "resources") | nindent 12 }}
          volumeMounts:
            - mountPath: /opt/nifi/nifi-scripts
              name: nifi-shared
            - mountPath: /opt/bundle-config
              name: dependencies
      volumes:
        - name: nifi-shared
          persistentVolumeClaim:
            claimName: nifi-data-claim
        - name: dependencies
          configMap:
            name: dependencies-configmap-{{ .Release.Name }}

            {{- include "extra_volumes" . | indent 10 }}
{{- end }}