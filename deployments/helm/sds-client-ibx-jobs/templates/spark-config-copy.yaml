{{- if .Values.jobOverrides.spark_config_copy_job.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: spark-config-copy-{{ .Release.Name}}
  labels:
    job: {{ .Values.labelJob }}
    {{- include "k8jobs.labels" . | nindent 4 }}
spec:
  backoffLimit: {{ .Values.backoffLimit }}
  activeDeadlineSeconds: {{ .Values.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ .Values.ttlSecondsAfterFinished }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
        {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
        azure.workload.identity/use: "true"
        {{ end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: deploy-jobs-sa
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ (index .Values "jobOverrides" "image" "repository") }}:{{ include "sparkconfigcopy.getImageTag" . }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with index .Values "jobOverrides" "spark_config_copy_job" }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
          command: {{ tpl (toYaml .command.aws) $ | nindent 12 }}
          {{- end }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
          command: {{ tpl (toYaml .command.azure) $ | nindent 12 }}
          {{- end }}
          {{- end }}              
     
          {{- if .Values.args }}
          args: {{ tpl (toYaml .Values.args) . | nindent 12 }}
          {{- end }}
          envFrom:
          {{- include "job_environment_from" . | default "\n  []" | indent 10 }}
          env:
          {{- include "custom_spark_config_copy_job_environment" . | indent 10 }}
          resources:
            {{- toYaml (index .Values "jobOverrides" "spark_config_copy_job" "resources") | nindent 12 }}


{{- end }}