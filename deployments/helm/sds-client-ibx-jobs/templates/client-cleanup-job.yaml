apiVersion: batch/v1
kind: Job
metadata:
  name: client-cleanup-job
  labels:
    {{- include "k8jobs.labels" . | nindent 4 }}
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation,HookSucceeded
spec:
  backoffLimit: {{ .Values.backoffLimit }}
  activeDeadlineSeconds: {{ .Values.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ .Values.ttlSecondsAfterFinished }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: cleanup-job-sa
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.initImage.repository }}:{{ .Values.initImage.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\";kubectl delete job -l job={{ .Values.labelJob }} -n {{ .Values.global.DEPLOY_NAMESPACE }} --ignore-not-found; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
          envFrom: []
          env: []
          resources:
            {{- toYaml .Values.resources | nindent 12 }}