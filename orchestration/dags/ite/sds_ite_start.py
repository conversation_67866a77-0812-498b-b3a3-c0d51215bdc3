"""
This is the HEAD DAG for ITE pipeline
"""
from datetime import datetime

from airflow.operators.dummy import DummyOperator
from commons.pe.sds_dag_orchestrator import SDSHeadDAG
from commons.ite.constants import IteConstants

with SDSHeadDAG(
        dag_id="sds_ite_start",
        description="DAG to start pipeline",
        pipeline_id=IteConstants.MODULE_NAME,
        start_date=datetime(2000, 1, 1),
        schedule_interval=None,
        render_template_as_native_obj=True,
        catchup=False,
        concurrency=1,
        tags=[IteConstants.MODULE_NAME, "QA_AUTOMATION"],
) as ite_start_dag:
    start_ite_pipeline = DummyOperator(task_id="start_pipeline")