{"id": "CFG-05.2-02", "title": "Devices are not jailbroken", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 6, "is_active": true, "description": "This assessment detects active servers and workstations that are jailbroken. Jailbroken devices pose security risks due to potential unauthorized modifications that can bypass controls and compromise system integrity.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Server and Workstation from Host where Origin contains MS Azure AD", "Validate that 'Jail Broken' is false"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,jail_broken,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server') and activity_status='Active' and ARRAY_CONTAINS(origin, 'MS Azure AD')", "success_condition": "jail_broken=false", "finding_primary_key": "p_id", "finding_title": "Jail<PERSON> <PERSON><PERSON> Detected", "finding_config": [{"title": "Jail Broken", "expression": "jail_broken", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["CFG-05.2"]}}