{{- if .Values.jobOverrides.em_api_config_deploy_job.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    job: {{ .Values.labelJob }}
    {{- include "k8jobs.labels" . | nindent 4 }}
  name: em-api-config-deploy-job-{{ .Release.Name}}
  namespace: {{ .Release.Namespace }}
spec:
  backoffLimit: {{ default .Values.backoffLimit .Values.jobOverrides.em_api_config_deploy_job.backoffLimit }}
  activeDeadlineSeconds: {{ default .Values.activeDeadlineSeconds .Values.jobOverrides.em_api_config_deploy_job.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ default .Values.ttlSecondsAfterFinished .Values.jobOverrides.em_api_config_deploy_job.ttlSecondsAfterFinished }}
  template:
    metadata:
      metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "k8jobs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: em-api-config-deploy-job
          {{- with index .Values "jobOverrides" "em_api_config_deploy_job" }}
          command: {{ tpl (toYaml .command) $ | nindent 12 }}
          {{- end }}
          env:
            {{- include "custom_em_api_config_deploy_job_environment" . | indent 10 }}
            {{- if .Values.global.SELF_SIGNED_CERT }}
            - name: REQUESTS_CA_BUNDLE
              value: /tmp/CABundle/pythoncacerts
              {{- end }}            
            - name: AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: clientId
            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ tpl .Values.secretName .}}
                  key: clientSecret
            - name: ENV_TYPE
              value: {{ .Values.global.CONFIG_CONTEXT }}
          image: "{{ (index .Values "jobOverrides" "em_api_config_deploy_job" "image" "repository") }}:{{ include "emdeploy.getImageTag" . }}"

          imagePullPolicy: IfNotPresent
          resources: {}
          volumeMounts:
            {{- if .Values.global.SELF_SIGNED_CERT }}
            - name: pythoncacerts
              mountPath: /tmp/CABundle
              {{- end }}
      volumes:
        {{- if .Values.global.SELF_SIGNED_CERT }}
        - name: pythoncacerts
          secret:
            secretName: pythoncacerts
            defaultMode: 511
          {{- end }}
  {{- end }}
