{"id": "MON-01-02", "title": "EC2 Instances with monitoring enabled", "contributing_module": ["Exposure", "Reporting"], "assessment_weightage": 8, "is_active": true, "description": "This assessment ensures that AWS EC2 instances have monitoring enabled. Instances without monitoring may reduce visibility into system activity and delay detection of security issues.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Server and Workstation from Host where Origin contains AWS", "Validate that 'AWS Instance Monitoring State' is Enabled"], "scope_query": "select p_id, array(struct(p_id,class,display_label,activity_status)) as affected_asset,aws_instance_monitoring_state,origin as affected_asset_origin from <%EI_SCHEMA_NAME%>.sds_ei__host__enrich where (type='Workstation' or type='Server') and activity_status='Active' and ARRAY_CONTAINS(origin,'AWS')", "success_condition": "aws_instance_monitoring_state='Enabled'", "finding_primary_key": "p_id", "finding_title": "EC2 Instance Without Monitoring Enabled", "finding_config": [{"title": "AWS Instance Monitoring State", "expression": "aws_instance_monitoring_state", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["MON-01"]}}