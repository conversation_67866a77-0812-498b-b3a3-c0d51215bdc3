{"id": "VPM-02-10", "title": "Software vulnerabilities on network devices are remediated within SLA", "contributing_module": ["Reporting"], "assessment_weightage": 1, "is_active": true, "description": "This assessment confirms that vulnerabilities on network devices are remediated within the defined SLA timelines, based on their severity and exploitability. Critical vulnerabilities must be remediated within 3 days. High severity exploitable vulnerabilities require remediation within 5 days, while non-exploitable ones have a 7-day SLA. Medium severity vulnerabilities must be addressed within 14 days if exploitable, and within 27 days if not. Low severity vulnerabilities should be remediated within 30 days if exploitable, and within 45 days if not.", "scope_entity": ["Host"], "scope_validation_steps": ["Select Active Network Device from Host", "Use 'Host Has Vulnerability Finding' relationship from Graph filter", "Validate that 'Vulnerability Patch SLA Breach Status' is false"], "scope_query": "SELECT host.p_id AS host_p_id, ARRAY(STRUCT(host.p_id, host.class, host.display_label, host.activity_status)) AS affected_asset, host.origin AS affected_asset_origin, COLLECT_SET(reln.vulnerability_patch_sla_breach_status) AS vulnerability_patch_sla_breach_status, COLLECT_SET(reln.current_status) AS vuln_status, COLLECT_SET(reln.vulnerability_breach_duration) AS vulnerability_breach_duration FROM <%EI_SCHEMA_NAME%>.sds_ei__host__enrich AS host LEFT JOIN <%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host__enrich AS reln ON host.p_id = reln.target_p_id LEFT JOIN <%EI_SCHEMA_NAME%>.sds_ei__vulnerability__enrich AS vuln ON reln.source_p_id = vuln.p_id WHERE host.activity_status = 'Active' AND host.type = 'Network Device' GROUP BY host.p_id, host.display_label, host.class, host.activity_status, host.origin", "success_condition": "CASE WHEN ARRAY_CONTAINS(vulnerability_patch_sla_breach_status, true) THEN false ELSE true END", "finding_primary_key": "host_p_id", "finding_title": "Overdue Vulnerability Remediation", "finding_config": [{"title": "Vulnerability Patch SLA Breach Status", "expression": "CASE WHEN ARRAY_CONTAINS(vulnerability_patch_sla_breach_status, true) THEN true ELSE false END", "finding_evidence": true}, {"title": "Vulnerability Finding Status", "expression": "CASE WHEN ARRAY_CONTAINS(vuln_status, 'Open') THEN 'Open' WHEN ARRAY_CONTAINS(vuln_status, 'Closed') THEN 'Closed' ELSE '' END", "finding_evidence": true}], "exposure_category": "Control Gap", "control_mapping": {"SCF": ["VPM-02", "VPM-04", "VPM-05.3", "EMB-07"], "PCI DSS v4.0.1": []}}